.el-tree {
    span {
        font-family:
            PingFangSC,
            PingFang SC;
        font-weight: 400;
        font-size: 14px;
        color: rgba(0, 0, 0, 0.85);
    }
    &-node {
        padding-bottom: 4px;
        &:last-child {
            padding-bottom: 0;
        }
        &__content {
            border-radius: 4px;
            &:hover {
                background-color: #f6f8fa !important;
            }
        }
        &__expand-icon {
            color: #595959;
            font-size: 14px;
            width: 26px;
            height: 26px;
            &.is-leaf {
                &:before {
                    content: '';
                }
            }
        }
        &:focus > .el-tree-node__content {
            background-color: unset;
        }
    }
}
.el-tree--highlight-current .el-tree-node.is-current > .el-tree-node__content {
    background-color: #1565ff1a !important;
}
