<template>
    <div class="base-info-content">
        <DocTable class="table-content" v-model="tableData" :config="tableConfig" />
    </div>
</template>

<script>
import DocTable from '@/script/components/tables/DocTable.vue';

export default {
    name: 'ToolBaseInfo',
    components: {
        DocTable
    },
    props: {
        value: {
            type: Object,
            required: true
        },
        isEdit: {
            type: Boolean,
            default: false
        }
    },
    data() {
        return {
            tableConfig: {
                mode: 'normal',
                labelWidth: 150,
                columns: [
                    {
                        label: '工具英文名称',
                        prop: 'toolNameEn'
                    },
                    {
                        label: '详细描述',
                        prop: 'toolDescription',
                        textarea: true,
                        lineHeight: '15rem'
                    },
                    {
                        label: '其他备注',
                        prop: 'comment',
                        textarea: true,
                        fit: true
                    }
                ],
                align: 'top',
                outBorder: false
            }
        };
    },
    computed: {
        tableData: {
            get() {
                return [
                    {
                        toolNameEn: this.value.toolNameEn,
                        toolDescription: this.value.toolDescription,
                        comment: this.value.comment
                    }
                ];
            },
            set(val) {
                this.$emit('input', {
                    ...this.value,
                    ...val[0]
                });
            }
        }
    }
};
</script>

<style lang="less" scoped>
.base-info-content {
    display: flex;
    gap: 1rem;
    .table-content {
        height: 100%;
        overflow: hidden;
        border-radius: 0.25rem;
        border: 0.0625rem solid #d6dae0;
    }
}
</style>
