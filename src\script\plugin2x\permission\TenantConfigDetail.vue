<template>
    <div class="tenant-config-detail">
        <header class="tenant-config-detail-header">
            <el-button type="plain" icon="el-icon-arrow-left" @click="$emit('back')"
                >返回
            </el-button>
            <span>租户配置详情</span>
        </header>
        <main class="tenant-config-detail-main">
            <aside class="main-left">
                <div
                    class="main-left-item"
                    v-for="(item, index) in asideMenus"
                    :key="index"
                    :class="{ 'main-left-item-active': item.value === activeValue }"
                    @click="handleAsideClick(item.value)"
                >
                    <img :src="activeValue === item.value ? item.iconActive : item.icon" alt="" />
                    <span>{{ item.label }}</span>
                </div>
            </aside>
            <div class="main-right custom-scrollbar" ref="scrollContainer">
                <section id="tenantBasicInfo" class="section-basic-info">
                    <p>租户基本信息</p>
                    <div class="section-content">
                        <DocTable class="table-content" v-model="tableData" :config="tableConfig" />
                    </div>
                </section>
                <section id="tenantApiInfo" class="section-api-info">
                    <p>调用密钥信息</p>
                    <div class="section-content">
                        <KeyManageTable
                            class="key-table"
                            v-model="detailValue.mcpSecretKeyList"
                            :readonly="true"
                        />
                    </div>
                </section>
                <section id="tenantMcpInfo" class="section-mcp-info">
                    <p>租户服务配置信息</p>
                    <div class="section-content">
                        <CardTable
                            :data="tenantMcpTableData"
                            :total="tenantMcpTableData.length"
                            :updateTable="getTenantMcpTableData"
                            :cardConfig="cardConfig"
                            :hidePagination="true"
                            @cardEvent="handleCardEvent"
                        ></CardTable>
                    </div>
                </section>
            </div>
        </main>
    </div>
</template>

<script>
import DocTable from '@/script/components/tables/DocTable.vue';
import KeyManageTable from './components/KeyManageTable.vue';
import CardTable from '@/script/components/cardTable/index.vue';
import { smoothScrollTo, scrollSpy } from '@/script/utils/method';
import Permission from '@/script/api/module/permission';

export default {
    name: 'TenantConfigDetail',
    components: {
        DocTable,
        KeyManageTable,
        CardTable
    },
    props: {
        tenantKey: {
            type: String,
            default: ''
        }
    },
    data() {
        return {
            scrollSpyDisabled: false,
            activeValue: 'tenantBasicInfo',
            asideMenus: [
                {
                    icon: require('@/img/common/file-close.png'),
                    iconActive: require('@/img/common/file-open.png'),
                    label: '租户基本信息',
                    value: 'tenantBasicInfo'
                },
                {
                    icon: require('@/img/common/key-close.png'),
                    iconActive: require('@/img/common/key-open.png'),
                    label: '调用密钥信息',
                    value: 'tenantApiInfo'
                },
                {
                    icon: require('@/img/common/tenant-close.png'),
                    iconActive: require('@/img/common/tenant-open.png'),
                    label: '租户服务配置信息',
                    value: 'tenantMcpInfo'
                }
            ],
            // 租户基本信息
            tableConfig: {
                mode: 'normal',
                labelWidth: 180,
                columns: [
                    { label: '租户信息：', prop: 'tenantName' },
                    { label: '租户密钥：', prop: 'tenantPwd' },
                    { label: '租户所属系统名称：', prop: 'sourceSystemName' },
                    { label: '是否开启工具鉴定：', prop: 'isToolAuth' },
                    { label: '其它备注：', prop: 'comment', singleLine: true }
                ],
                align: 'top',
                pairCount: 2,
                outBorder: false
            },
            // 租户调用密钥信息
            detailValue: {
                tenantName: '',
                tenantPwd: '',
                sourceSystemName: '',
                isToolAuth: '',
                comment: '',
                mcpSecretKeyList: {
                    originList: []
                }
            },
            // 租户服务配置信息
            // 卡片配置
            cardConfig: {
                module: 'permission',
                icon: {
                    0: require('../../../img/common/service-inactive.png'),
                    1: require('../../../img/common/service-active.png')
                },
                diyStyle: {
                    border: '1px solid #d6dae0'
                },
                readonly: true,
                headerHideItem: [
                    'edit',
                    'cancel',
                    'save',
                    'delete',
                    'setting',
                    'tenantKey',
                    'validate',
                    'tag',
                    'copy'
                ],
                navList: [
                    {
                        name: '基础信息',
                        value: 'ServiceBaseInfo'
                    },
                    {
                        name: '关联工具',
                        value: 'ServiceToolInfo'
                    },
                    {
                        name: '关联资源',
                        value: 'ServiceResourceInfo'
                    }
                ]
            },
            tenantMcpTableData: []
        };
    },
    computed: {
        tableData: {
            get() {
                return [
                    {
                        tenantName: this.detailValue.tenantName || '',
                        tenantPwd: this.detailValue.tenantPwd || '',
                        sourceSystemName: this.detailValue.sourceSystemName || '',
                        isToolAuth: this.getNumBool(this.detailValue.isToolAuth),
                        comment: this.detailValue.comment || ''
                    }
                ];
            }
        }
    },
    created() {
        this.getTenantDetails();
        this.getTenantMcpTableData();
    },
    methods: {
        getNumBool(val) {
            if (val === 1) {
                return '是';
            } else if (val === 0) {
                return '否';
            }
            return val;
        },
        getTenantDetails() {
            Permission.getTenantDetails({ tenantKey: this.tenantKey }).then((res) => {
                if (res.serviceFlag === 'TRUE') {
                    this.detailValue = {
                        ...res.data,
                        mcpSecretKeyList: {
                            originList: res.data.mcpSecretKeyList
                        }
                    };
                } else {
                    this.$message.error(res.returnMsg || '获取租户配置详情失败');
                }
            });
        },
        getTenantMcpTableData() {
            Permission.getTenantMcpInfo({ tenantKey: this.tenantKey }).then((res) => {
                if (res.serviceFlag === 'TRUE') {
                    this.tenantMcpTableData = res.data.list.map((item) => {
                        return {
                            rawData: item,
                            id: item.mcpId,
                            name: item.mcpName,
                            description: item.mcpDescription,
                            isValid: item.isValid,
                            createdTime: item.createdTime,
                            lastUpdateTime: item.lastUpdateTime
                        };
                    });
                } else {
                    this.$message.error(res.returnMsg || '获取租户服务配置信息失败');
                }
            });
        },
        handleCardEvent(event) {
            const eventMap = {
                // 展开卡片
                expand: () => {
                    const mcpData = this.tenantMcpTableData.find(
                        (item) => item.id === event.params.id
                    );
                    if (!mcpData) {
                        return;
                    }
                    const configInfo = mcpData.rawData.configInfo;
                    const responseData = {
                        ...mcpData.rawData,
                        configInfo:
                            (typeof configInfo === 'object' &&
                                JSON.stringify(configInfo, null, 4)) ||
                            JSON.stringify(JSON.parse(configInfo), null, 4),
                        resourceList: mcpData.rawData.resourceList || [],
                        toolList: mcpData.rawData.toolList || []
                    };
                    event.callback(JSON.parse(JSON.stringify(responseData)));
                }
            };

            eventMap[event.type] && eventMap[event.type]();
        },
        handleServiceResponse(res, successMsg, errorMsg) {
            if (res.serviceFlag === 'TRUE') {
                this.$message.success(res.returnMsg || successMsg);
                this.handleSearch();
            } else {
                this.$message.error(res.returnMsg || errorMsg);
            }
        },
        handleAsideClick(value) {
            this.scrollSpyDisabled = true;
            this.activeValue = value;
            smoothScrollTo(value, 1.5, 'rem');
            setTimeout(() => {
                this.scrollSpyDisabled = false;
            }, 500);
        },
        initScrollSpy() {
            const container = this.$refs.scrollContainer;
            if (!container) return;
            this.cleanupScrollSpy = scrollSpy(
                container,
                'section',
                (id) => {
                    if (!this.scrollSpyDisabled) {
                        this.activeValue = id;
                    }
                },
                36
            );
        },
        destroyScrollSpy() {
            if (typeof this.cleanupScrollSpy === 'function') {
                this.cleanupScrollSpy();
                this.cleanupScrollSpy = null;
            }
        }
    },
    mounted() {
        this.$nextTick(this.initScrollSpy);
    },
    beforeDestroy() {
        this.destroyScrollSpy();
    }
};
</script>

<style scoped lang="less">
.tenant-config-detail {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    gap: 1rem;
    &-header {
        height: 3.5rem;
        background: rgba(255, 255, 255, 0.96);
        border-radius: 0.375rem;
        display: flex;
        align-items: center;
        gap: 0.75rem;
        padding: 0 1rem;
        font-family:
            PingFangSC,
            PingFang SC;
        font-weight: 600;
        font-size: 1rem;
        color: rgba(0, 0, 0, 0.85);
    }
    &-main {
        height: 0;
        flex: 1;
        display: flex;
        gap: 1rem;
        .main-left {
            width: 18rem;
            height: 100%;
            background: rgba(255, 255, 255, 0.96);
            border-radius: 0.375rem;
            display: flex;
            flex-direction: column;
            gap: 0.5rem;
            padding: 1.5rem 1rem;
            &-item {
                height: 2.5rem;
                border-radius: 0.375rem;
                display: flex;
                align-items: center;
                gap: 0.625rem;
                padding: 0 1rem;
                cursor: pointer;
                img {
                    width: 1.25rem;
                    height: 1.25rem;
                }
                span {
                    font-family:
                        PingFangSC,
                        PingFang SC;
                    font-weight: 400;
                    font-size: 0.875rem;
                    color: rgba(0, 0, 0, 0.85);
                }
                &-active {
                    background: #1565ff1a;
                    span {
                        font-weight: 500;
                        color: #1565ff;
                    }
                }
            }
        }
        .main-right {
            min-width: 0;
            flex: 1;
            height: 100%;
            overflow-y: auto;
            background: rgba(255, 255, 255, 0.96);
            border-radius: 0.375rem;
            padding: 1.5rem;
            section {
                &:not(:last-child) {
                    margin-bottom: 1.5rem;
                    border-bottom: 0.0625rem solid #ebedf0;
                }
                p {
                    height: 1.5rem;
                    font-family:
                        PingFangSC,
                        PingFang SC;
                    font-weight: 600;
                    font-size: 1.125rem;
                    color: rgba(0, 0, 0, 0.85);
                    margin: 0;
                    display: flex;
                    align-items: center;
                    gap: 1rem;
                    .p-btn {
                        font-size: 1.125rem;
                        line-height: 1.45rem;
                        color: #fff;
                        cursor: pointer;
                        display: flex;
                        justify-content: center;
                        width: 1.5rem;
                        height: 1.5rem;
                        background: #1565ff;
                        border-radius: 50%;
                        user-select: none;
                    }
                }
                .section-content {
                    padding: 1.5rem 0;
                    padding-right: 2rem;
                    .table-content {
                        overflow: hidden;
                        border-radius: 0.25rem;
                        border: 0.0625rem solid #d6dae0;
                    }
                    .key-table {
                        min-height: 10rem;
                    }
                }
            }
        }
    }
}
</style>
