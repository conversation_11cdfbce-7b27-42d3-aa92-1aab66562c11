<template>
    <div class="main-index-page mcpservice-theme">
        <NavBar @jumpRouter="jumpRouter"></NavBar>
        <div class="main-index-container">
            <router-view></router-view>
        </div>
    </div>
</template>
<script>
import NavBar from './components/NavBar.vue';
import { styleMixin } from '@/script/mixin/styleMixin';

export default {
    name: 'MainIndex',
    components: {
        NavBar
    },
    mixins: [styleMixin],
    methods: {
        jumpRouter(val) {
            this.openSubPath(val, '', {});
        }
    }
};
</script>
<style scoped lang="less">
.main-index-page {
    height: 100%;
    width: 100%;
    display: flex;
    flex-direction: column;
    .main-index-container {
        padding: 1rem;
        width: 100%;
        height: 0;
        flex: 1;
        background-image: url('../../../img/homePage/home-bg.png');
        background-size: 100% 100%;
        background-repeat: no-repeat;
    }
}
</style>
