<template>
    <div class="json-import">
        <div class="warning-tip">
            <i class="el-icon-warning"></i>
            当前仅支持导入OpenAPI 3规格式的JSON文件
        </div>

        <textarea
            v-model.trim="jsonContent"
            :placeholder="jsonPlaceholder"
            class="json-content custom-scrollbar"
            type="textarea"
            :rows="16"
            :resize="false"
        ></textarea>

        <div class="dialog-footer">
            <el-button @click="handleCancel">取消</el-button>
            <el-button type="primary" @click="handleConfirm">确定</el-button>
        </div>
    </div>
</template>

<script>
export default {
    name: 'ByJson',
    data() {
        return {
            jsonContent: '',
            jsonError: false,
            jsonPlaceholder:
                '// 示例 JSON\n' +
                JSON.stringify(
                    {
                        mcpServers: {
                            '12306-mcp': {
                                type: 'sse',
                                url: 'https://mcp.api-inference.modelscope.net/sse?mcpid=citymove078e38&mcpToken={your key}'
                            }
                        }
                    },
                    null,
                    2
                )
        };
    },
    methods: {
        // 自动格式化JSON并验证JSON格式
        formatJson() {
            try {
                const parsed = JSON.parse(this.jsonContent);

                if (typeof parsed !== 'object' || parsed === null) {
                    throw new Error('必须是对象或数组');
                }

                this.jsonContent = JSON.stringify(JSON.parse(this.jsonContent), null, 2);
                this.jsonError = false;
            } catch (e) {
                this.jsonError = true;
                this.$message.error(`JSON格式错误: ${e.message}`);
            }
        },
        // 确认导入
        handleConfirm() {
            if (this.jsonContent.trim() === '') {
                this.$message.warning('当前JSON内容为空');
                return;
            }
            this.formatJson();
            if (this.jsonError) return;
            // 发送结果
            const result = {
                success: true,
                data: this.jsonContent
            };

            this.$emit('on-success', result);
        },
        // 取消导入
        handleCancel() {
            this.$emit('on-cancel');
        }
    }
};
</script>

<style lang="less" scoped>
.json-import {
    padding: 0 20px;

    .warning-tip {
        background: #fff6e6;
        border: 1px solid #ffe58f;
        padding: 10px 12px;
        margin-bottom: 20px;
        display: flex;
        align-items: center;
        font-family:
            PingFangSC,
            PingFang SC;
        font-weight: 400;
        font-size: 14px;
        color: rgba(0, 0, 0, 0.65);

        .el-icon-warning {
            color: #ff7802;
            margin-right: 8px;
        }
    }
    .json-content {
        width: 100%;
        height: 352px;
        outline: none;
        resize: none;
        background: #f6f8fa;
        border-radius: 4px;
        border: 1px dashed #d6dae0;
        font-size: 14px;
        padding: 16px;
    }

    .dialog-footer {
        text-align: right;
        margin-top: 20px;
    }
}
</style>
