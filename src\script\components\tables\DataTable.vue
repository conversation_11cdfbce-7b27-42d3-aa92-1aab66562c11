<!-- 数据表格 -->
<template>
    <div class="data-table" ref="tables">
        <el-table ref="myTable" :size="size" style="width: 100%" v-bind="$attrs" v-on="$listeners">
            <!-- 自定义空内容 -->
            <template #empty>
                <slot name="empty"></slot>
            </template>
            <!-- 列 -->
            <el-table-column
                v-if="isSelectable"
                type="selection"
                reserve-selection
                width="36"
                :selectable="selectable"
            />
            <el-table-column v-for="item in columns" v-bind="item" :key="item.prop">
                <!-- 添加自定义表头插槽 -->
                <template #header>
                    <slot :name="`header-${item.prop}`">
                        {{ item.label }}
                    </slot>
                </template>

                <template #default="{ row, $index }">
                    <slot :name="item.prop" :row="row" :inx="$index">
                        {{ row[item.prop] }}
                    </slot>
                </template>
            </el-table-column>
        </el-table>
        <!-- 分页 -->
        <div class="wrap-pagination" v-if="showPagination">
            <slot name="pagination">
                <el-pagination
                    class="pagination"
                    :current-page="pagination.curPage"
                    :page-sizes="pagination.pageSizes"
                    :page-size="pagination.pageSize"
                    :total="total"
                    :layout="layout"
                    :pager-count="pagination.pagerCount"
                    background
                    @size-change="handleSizeChange"
                    @current-change="handleCurrentChange"
                />
            </slot>
        </div>
    </div>
</template>

<script>
export default {
    name: 'DataTable',
    inheritAttrs: false,
    props: {
        columns: {
            type: Array,
            default: () => [],
            required: true
        },
        size: {
            type: String,
            default: 'small'
        },
        updateTable: {
            type: Function,
            default: () => {},
            validator(value) {
                return typeof value === 'function';
            }
        },
        total: {
            type: Number,
            validator: (value) => {
                return value >= 0;
            }
        },
        layout: {
            type: String,
            default: 'total, prev, pager, next, sizes, jumper'
        },
        isSelectable: {
            type: Boolean,
            default: false
        },
        showPagination: {
            type: Boolean,
            default: true
        },
        pagination: {
            type: Object,
            default: () => ({
                curPage: 1,
                pageSize: 10
            })
        },
        selectable: {
            type: Function
        }
    },
    data() {
        return {};
    },
    created() {
        this.initPagination();
    },
    methods: {
        initPagination() {
            const { pageSize, pageSizes, pagerCount } = this.pagination;
            const mapPageSizes = {
                10: [10, 15, 25, 40],
                15: [15, 20, 30, 50]
            };
            if (!pageSizes || !pageSizes.length) {
                this.pagination.pageSizes = mapPageSizes[pageSize];
            }
            if (!pagerCount) {
                this.pagination.pagerCount = 7;
            }
        },
        handleSizeChange(pageSize) {
            Object.assign(this.pagination, {
                curPage: 1,
                pageSize
            });
            this.updateTable({
                curPage: 1,
                pageSize
            });
        },
        handleCurrentChange(curPage) {
            this.pagination.curPage = curPage;
            this.updateTable({
                curPage,
                pageSize: this.pagination.pageSize
            });
        },
        scrollToTop() {
            this.$nextTick(() => {
                const tableBody = this.$refs.myTable.$el.querySelector('.el-table__body-wrapper');
                if (tableBody) {
                    tableBody.scrollTop = 0; // 滚动到最上方
                }
            });
        },
        cleanSelect() {
            this.$refs.myTable.clearSelection();
        },
        toggleRowSelection(row, selected) {
            this.$refs.myTable.toggleRowSelection(row, selected);
        }
    }
};
</script>

<style lang="less" scoped>
.data-table {
    height: 100%;
    display: flex;
    flex-flow: column;
    /deep/ .el-table {
        flex: 1;
        width: 100%;
        &::before {
            background: #ebedf0;
        }
        &::after {
            background: none;
        }
        .cell {
            font-family:
                PingFangSC,
                PingFang SC;
            font-weight: 400;
            font-size: 0.875rem;
            line-height: 1.5rem;
        }
        .el-table__header-wrapper {
            width: 100%;
            th {
                border: none;
                background: #f6f8fa;
                color: rgba(0, 0, 0, 0.85);
            }
        }
        .el-table__body-wrapper {
            height: calc(100% - 2rem);
            overflow-y: auto;
            td {
                color: rgba(0, 0, 0, 0.65);
            }
            // 自定义滚动条样式
            &::-webkit-scrollbar {
                width: 0.375rem;
            }
            &::-webkit-scrollbar-thumb {
                border-radius: 0.5rem;
                background: #ebedf0;
            }
            &::-webkit-scrollbar-track {
                /* 滚动条里面轨道 */
                border-radius: 0.5rem;
                background: transparent;
            }
            &::-webkit-scrollbar-corner {
                background: rgba(0, 0, 0, 0);
            }
        }
    }
    .wrap-pagination {
        margin-top: 1rem;
        text-align: right;
        height: 2rem;
    }
}

/deep/.el-pagination.pagination {
    height: 100%;
    display: flex;
    justify-content: flex-end;
    align-items: center;
    margin: 0;
    padding: 0;
    font-family:
        PingFangSC,
        PingFang SC;
    font-weight: 400;
    font-size: 0.875rem;
    color: rgba(0, 0, 0, 0.65);
    line-height: 1.375rem;
    .el-pagination__total {
        margin-right: auto;
        font-family:
            PingFangSC,
            PingFang SC;
        font-weight: 400;
        font-size: 0.875rem;
        color: rgba(0, 0, 0, 0.65);
        line-height: 1.375rem;
    }
    .btn-prev,
    .btn-next,
    .el-pager li {
        width: 1.875rem;
        background-color: transparent;
        border-radius: 0.125rem;
        border: 1px solid #d6dae0;
        font-family:
            PingFangSC,
            PingFang SC;
        font-weight: 400;
        color: rgba(0, 0, 0, 0.65);
        &.active {
            background: #1565ff;
            border: none;
        }
    }
    .el-pagination__sizes,
    .el-pagination__jump {
        display: flex;
        align-items: center;
        font-family:
            PingFangSC,
            PingFang SC;
        font-weight: 400;
        color: rgba(0, 0, 0, 0.65);
        .el-select {
            height: 100%;
        }
        .el-input {
            height: 100%;
            &__inner {
                height: 100%;
                border-radius: 0.125rem;
                border: 1px solid #d6dae0;
            }
        }
    }
}
</style>
