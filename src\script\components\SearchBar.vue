<template>
    <el-form ref="searchBarRef" class="search-bar" :model="form" size="small">
        <el-row v-for="(cols, inx) in formCols" :key="inx" :gutter="16">
            <template
                v-for="(
                    {
                        isShow,
                        prop,
                        label,
                        span,
                        type,
                        isDisabled,
                        rules,
                        labelWidth,
                        attrs,
                        listeners,
                        opts
                    },
                    i
                ) in Array.isArray(cols) ? cols : [cols]"
            >
                <el-col v-if="isShow" :key="`${prop}${inx}`" :span="span">
                    <el-form-item
                        :class="{
                            noBackground: type === 'tool',
                            isDisabled: isDisabled,
                            'mb-0': inx === formCols.length - 1 && i === cols.length - 1
                        }"
                        :label="label"
                        :prop="prop"
                        :rules="rules"
                        :label-width="labelWidth"
                    >
                        <template #error="{ error }">
                            <span class="custom-error">
                                {{ error }}
                            </span>
                        </template>
                        <el-select
                            v-if="type === 'select' || type === 'el-select'"
                            v-model="form[prop]"
                            class="w-full"
                            popper-class="mcpservice-theme"
                            v-bind="attrs"
                            v-on="listeners"
                        >
                            <el-option v-for="it in opts" v-bind="it" :key="it.value" />
                        </el-select>
                        <template v-else-if="type === 'tool'">
                            <slot></slot>
                        </template>
                        <template v-else-if="type === 'slot'">
                            <div class="name-slot">
                                <slot :name="prop"></slot>
                            </div>
                        </template>
                        <component
                            v-else
                            :is="type"
                            v-model="form[prop]"
                            v-bind="attrs"
                            v-on="listeners"
                        />
                    </el-form-item>
                </el-col>
            </template>
        </el-row>
    </el-form>
</template>

<script>
export default {
    name: 'SearchBar',
    components: {
        DropdownCheckBox: () => import('@/script/components/DropdownCheckBox.vue')
    },
    props: {
        formCols: {
            type: Array,
            default: () => []
        },
        form: {
            type: Object,
            default: () => ({})
        }
    },
    methods: {
        async validForm() {
            try {
                return await this.$refs.searchBarRef.validate();
            } catch (err) {
                return err;
            }
        },
        async validateField(prop) {
            try {
                return await this.$refs.searchBarRef.validateField(prop);
            } catch (err) {
                return err;
            }
        },
        async clearValidate(prop) {
            try {
                return await this.$refs.searchBarRef.clearValidate(prop);
            } catch (err) {
                return err;
            }
        }
    }
};
</script>

<style lang="less" scoped>
.search-bar {
    width: 100%;
    display: flex;
    flex-direction: column;
    gap: 16px;
    .el-row {
        .el-col:last-of-type .el-form-item {
            margin-right: 0;
        }
    }
    .el-form-item {
        margin: 0;
        background-color: transparent;
        .custom-error {
            position: absolute;
            right: 0;
            top: -28px;
            padding: 0 6px;
            height: 20px;
            line-height: 20px;
            border-radius: 2px;
            font-size: 12px;
            color: #fff;
            background-color: #f56c6c;
            z-index: 999;
            &::before {
                content: '';
                position: absolute;
                width: 0;
                height: 0;
                right: 50%;
                bottom: -6px;
                transform: translateX(50%);
                border-left: 8px solid transparent;
                border-right: 8px solid transparent;
                border-top: 8px solid #f56c6c;
            }
        }
        &.noBackground {
            border: none;
            background-color: transparent;
        }
        /deep/ .el-form-item__label:before {
            margin-right: 2px !important;
        }
        &.isDisabled {
            /deep/ .el-form-item__label {
                background-color: #f5f7fa;
            }
        }
        /deep/ .el-form-item__label {
            align-items: center;
            position: relative;
            margin-bottom: 0;
            padding-right: 10px;
            font-family:
                PingFangSC,
                PingFang SC;
            font-weight: 400;
            font-size: 14px;
            color: rgba(0, 0, 0, 0.85);
        }
        /deep/ .el-form-item__content {
            position: relative;
            display: flex;
        }
    }
}
.mb-0 {
    margin-bottom: 0 !important;
}
.w-full {
    width: 100%;
}
/deep/.el-date-editor.el-input {
    width: 100%;
}
/deep/.el-range-editor.el-input__inner {
    width: 100%;
}
.name-slot {
    width: 100%;
    position: relative;
}
</style>
