const token = localStorage.getItem('token');
import { rel_operateUserId, rel_sourceSystemId, rel_sourceSystemName } from '../common/baseCompExt';
import GatewayAxios from '../common/gatewayAxios.js';

// 后端网关
const otherGateWay = '/mcp-service';

// 通用post请求
const norMalConfig = function (url, params = {}, gateWay) {
    const API = new GatewayAxios({
        rel_operateUserId,
        rel_sourceSystemId,
        rel_sourceSystemName,
        gateWay: `${gateWay}${otherGateWay}`
    });
    return new Promise((resolve, reject) => {
        let newParams = {
            operateUserId: rel_operateUserId,
            webToken: token,
            sourceSystemName: rel_sourceSystemName,
            sourceSystemId: rel_sourceSystemId,
            requestData: params
        };
        API.post(url, newParams)
            .then((res) => {
                resolve(res);
            })
            .catch((err) => {
                console.log('err: ', err);
                reject(err);
            });
    });
};

export default norMalConfig;

// 文件上传
export const fileConfig = function (url, params, options) {
    const API = new GatewayAxios({ rel_operateUserId, rel_sourceSystemId, rel_sourceSystemName });
    return new Promise((resolve, reject) => {
        let newParams = {
            operateUserId: rel_operateUserId,
            webToken: token,
            sourceSystemName: rel_sourceSystemName,
            sourceSystemId: rel_sourceSystemId,
            requestData: params
        };
        API.getFile(url, newParams, options)
            .then((res) => {
                resolve(res);
            })
            .catch((err) => {
                console.log('err: ', err);
                reject(err);
            });
    });
};
// 通用get请求
export const norMalConfigGet = function (url, params = {}, gateWay) {
    const API = new GatewayAxios({
        rel_operateUserId,
        rel_sourceSystemId,
        rel_sourceSystemName,
        gateWay: `${gateWay}${otherGateWay}`
    });
    return new Promise((resolve, reject) => {
        let newParams = {
            operateUserId: rel_operateUserId,
            webToken: token,
            sourceSystemName: rel_sourceSystemName,
            sourceSystemId: rel_sourceSystemId,
            requestData: params
        };
        API.get(url, newParams)
            .then((res) => {
                resolve(res);
            })
            .catch((err) => {
                console.log('err: ', err);
                reject(err);
            });
    });
};
