<template>
    <section class="section-card">
        <div class="section-card-header">
            <span>{{ title }}</span>
            <el-radio-group
                class="header-radio-group"
                v-bind="$attrs"
                v-on="$listeners"
                size="small"
                v-if="radioGroup.length > 0"
            >
                <el-radio-button
                    v-for="(item, index) in radioGroup"
                    :key="index"
                    :label="item.value"
                >
                    {{ item.label }}
                </el-radio-button>
            </el-radio-group>
        </div>
        <div class="section-card-content">
            <slot></slot>
        </div>
    </section>
</template>
<script>
export default {
    name: 'SectionCard',
    props: {
        title: {
            type: String,
            default: '标题'
        },
        radioGroup: {
            type: Array,
            default: () => []
        }
    }
};
</script>
<style scoped lang="less">
.section-card {
    background: rgba(255, 255, 255, 0.96);
    border-radius: 0.375rem;
    padding: 1rem;
    display: flex;
    flex-direction: column;
    &-header {
        margin-bottom: 1rem;
        font-family:
            PingFangSC,
            PingFang SC;
        font-weight: 600;
        font-size: 1rem;
        color: rgba(0, 0, 0, 0.85);
        line-height: 1.25rem;
        position: relative;
        .header-radio-group {
            position: absolute;
            right: 0;
            top: 50%;
            transform: translateY(-50%);
            height: 2rem;
            /deep/.el-radio-button {
                margin: 0;
                height: 100%;
                &.is-active {
                    .el-radio-button__inner {
                        font-weight: 500;
                        color: #1565ff;
                        border-color: #1565ff;
                        background-color: #1565ff1a;
                        border-radius: 0.25rem;
                    }
                }
                &__inner {
                    font-family:
                        PingFangSC,
                        PingFang SC;
                    font-weight: 400;
                    font-size: 0.875rem;
                    color: rgba(0, 0, 0, 0.65);
                    line-height: 1.25rem;
                    padding: 0 1.25rem;
                    height: 100%;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                }
            }
        }
    }
    &-content {
        height: 0;
        flex: 1;
    }
}
</style>
