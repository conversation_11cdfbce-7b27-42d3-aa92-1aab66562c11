import norMalConfig from '../normal-config';
const gateWay = '/selfanalyticsSevice'; // 19901端口:/mcpservice, 29000端口:/selfanalyticsSevice

export default {
    // 资源列表
    getResourcePage(params) {
        return norMalConfig('/resource/page', params, gateWay);
    },
    // 资源生效状态
    changeResourceValid(params) {
        return norMalConfig('/resource/changeValid', params, gateWay);
    },
    // 资源新增
    addResource(params) {
        return norMalConfig('/resource/add', params, gateWay);
    },
    // 资源信息修改
    updateResource(params) {
        return norMalConfig('/resource/update', params, gateWay);
    },
    // 查询详情
    getResourceDetails(params) {
        return norMalConfig('/resource/getDetails', params, gateWay);
    },
    // 资源删除
    removeResource(params) {
        return norMalConfig('/resource/remove', params, gateWay);
    }
};
