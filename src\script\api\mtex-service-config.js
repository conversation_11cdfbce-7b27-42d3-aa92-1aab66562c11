import axios from 'axios';
const token = localStorage.getItem('token');
import { reqUtil } from 'mtex-rams-core';
const { decodeResult, tryEncodeParam } = reqUtil;
import { decodeResultUser } from '../utils/resUtils';

// 由于平台组没有租户概念，所以没有使用GatewayAxios
// 平台组 post 请求
const mtexFun = function (url, params = {}) {
    let newParams = params;
    newParams = tryEncodeParam(newParams);
    return new Promise((resolve, reject) => {
        axios
            .post(url, newParams, {
                headers: {
                    token: token
                }
            })
            .then((res) => {
                let newRes = res.data;
                if (res.data.encodeResp) {
                    if (res.config.url === '/mtex/auth/um/queryUserPermissions') {
                        newRes = decodeResultUser(res.data); //特殊处理，返回结果解密
                    } else {
                        newRes = decodeResult(res.data);
                    }
                }
                let result = newRes;
                if (result.success == '0') {
                    resolve(result.result);
                    return;
                }
                if (res.headers && res.headers['content-type'] === 'application/force-download') {
                    resolve(result);
                    return;
                }
                reject({
                    success: result.success,
                    errorMessage: result.errorInfo.message,
                    result: result
                });
                return;
            })
            .catch((err) => {
                reject({
                    success: 111,
                    errorMessage: err.message
                });
            });
    });
};
// 平台组get 请求
export const mtexFunGet = function (url, params = {}, headers = undefined) {
    let newParams = params;
    newParams = tryEncodeParam(newParams);
    if (headers) {
        for (let key in headers) {
            const value = headers[key];
            axios.defaults.headers.get[key] = value;
        }
    }
    return new Promise((resolve, reject) => {
        axios
            .get(url, newParams, {
                headers: {
                    token: token
                }
            })
            .then((res) => {
                let newRes = res.data;
                if (res.data.encodeResp) {
                    newRes = decodeResultUser(res.data);
                }
                resolve(newRes);
            })
            .catch((err) => {
                reject('');
            });
    });
};
export default mtexFun;
