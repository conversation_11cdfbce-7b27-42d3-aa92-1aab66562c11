import norMalConfig from '../normal-config';
const gateWay = '/selfanalyticsSevice';

export default {
    // MCP服务列表
    getMCPInfoPage(params) {
        return norMalConfig('/mcpInfo/page', params, gateWay);
    },
    // 关联工具配置查询
    getMCPToolConfigList(params) {
        return norMalConfig('/mcpInfo/tool/config/list', params, gateWay);
    },
    // 关联资源配置查询
    getMCPResourceConfigList(params) {
        return norMalConfig('/mcpInfo/resource/config/list', params, gateWay);
    },
    // 服务关联工具配置保存
    saveMCPToolConfig(params) {
        return norMalConfig('/mcpInfo/tool/config/save', params, gateWay);
    },
    // 服务关联资源配置保存
    saveMCPResourceConfig(params) {
        return norMalConfig('/mcpInfo/resource/config/save', params, gateWay);
    },
    // 服务生效状态
    changeMCPValid(params) {
        return norMalConfig('/mcpInfo/changeValid', params, gateWay);
    },
    // 查询MCP服务详情
    getMCPDetails(params) {
        return norMalConfig('/mcpInfo/getDetails', params, gateWay);
    },
    // 查看工具叶子节点参数详情接口
    getToolDetails(params) {
        return norMalConfig('/toolInfo/getDetails', params, gateWay);
    },
    // 服务关联资源删除
    delMCPResourceRelation(params) {
        return norMalConfig('/mcpInfo/delResourceRelation', params, gateWay);
    },
    // 服务关联工具删除
    delMCPToolRelation(params) {
        return norMalConfig('/mcpInfo/delToolRelation', params, gateWay);
    },
    // 服务新增
    addMCPInfo(params) {
        return norMalConfig('/mcpInfo/add', params, gateWay);
    },
    // 服务信息修改
    updateMCPInfo(params) {
        return norMalConfig('/mcpInfo/update', params, gateWay);
    },
    // 服务删除
    deleteMCPInfo(params) {
        return norMalConfig('/mcpInfo/remove', params, gateWay);
    }
};
