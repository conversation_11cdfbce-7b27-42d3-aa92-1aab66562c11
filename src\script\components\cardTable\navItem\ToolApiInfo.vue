<template>
    <div class="api-info-content">
        <DocTable class="table-content" v-model="tableData" :config="tableConfig" />
    </div>
</template>

<script>
import DocTable from '@/script/components/tables/DocTable.vue';

export default {
    name: 'ToolApiInfo',
    components: {
        DocTable
    },
    props: {
        value: {
            type: Object,
            required: true
        },
        isEdit: {
            type: Boolean,
            default: false
        }
    },
    data() {
        return {
            tableConfig: {
                mode: 'normal',
                labelWidth: 150,
                columns: [
                    { label: '服务编码：', prop: 'serviceCode' },
                    { label: '服务名称：', prop: 'serviceName' },
                    { label: '服务所属项目：', prop: 'serviceProject' },
                    { label: '服务所属模块：', prop: 'serviceModule' },
                    { label: '服务地址：', prop: 'serviceUri', singleLine: true },
                    { label: '服务IP：', prop: 'serviceIp', singleLine: true },
                    { label: '服务port：', prop: 'servicePort', singleLine: true },
                    { label: '服务调用方式：', prop: 'serviceMethod', singleLine: true },
                    { label: '是否集团框架：', prop: 'isCmccFramework', singleLine: true },
                    { label: '是否起效：', prop: 'isValid', singleLine: true },
                    {
                        label: '其他备注：',
                        prop: 'serviceComment',
                        singleLine: true,
                        textarea: true,
                        fit: true
                    }
                ],
                align: 'top',
                pairCount: 2,
                outBorder: false
            }
        };
    },
    computed: {
        tableData: {
            get() {
                return [
                    {
                        serviceCode: this.value.serviceCode || '',
                        serviceName: this.value.serviceName || '',
                        serviceProject: this.value.serviceProject || '',
                        serviceModule: this.value.serviceModule || '',
                        serviceUri: this.value.serviceUri || '',
                        serviceIp: this.value.serviceIp || '',
                        servicePort: this.value.servicePort || '',
                        serviceMethod: this.value.serviceMethod || '',
                        isCmccFramework: this.getNumBool(this.value.isCmccFramework),
                        isValid: this.getNumBool(this.value.isValid),
                        serviceComment: this.value.serviceComment || ''
                    }
                ];
            },
            set(val) {
                this.$emit('input', {
                    ...this.value,
                    ...val[0]
                });
            }
        }
    },
    methods: {
        getNumBool(val) {
            if (val === 1) {
                return '是';
            } else if (val === 0) {
                return '否';
            }
            return val;
        }
    }
};
</script>

<style lang="less" scoped>
.api-info-content {
    display: flex;
    gap: 1rem;
    .table-content {
        height: 100%;
        overflow: hidden;
        border-radius: 0.25rem;
        border: 0.0625rem solid #d6dae0;
    }
}
</style>
