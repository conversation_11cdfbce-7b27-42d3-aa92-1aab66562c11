<template>
    <div class="home-container">
        <header class="home-container-header">首页</header>
        <div class="home-container-search">
            <el-radio-group class="home-container-search-radio" v-model="radioValue" size="small">
                <el-radio-button v-for="item in radioList" :key="item" :label="item.value">
                    {{ item.label }}
                </el-radio-button>
            </el-radio-group>
            <el-date-picker
                v-if="radioValue === 'custom'"
                class="home-container-search-date"
                v-model="dateValue"
                type="daterange"
                range-separator="-"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                value-format="yyyy-MM-dd"
                @change="handleDateChange"
            >
            </el-date-picker>
        </div>
        <main class="home-container-main">
            <div class="main-left w-57">
                <SectionCard title="MCP服务使用情况">
                    <div class="mcp-usage-card-group">
                        <MetricCard v-for="item in mcpUsageCards" :key="item.title" v-bind="item" />
                    </div>
                </SectionCard>
                <SectionCard class="tenant-service-call-times" title="租户服务调用次数">
                    <CommonEchart
                        :targetData="serviceCallTimesData"
                        refName="serviceCallTimes"
                    ></CommonEchart>
                </SectionCard>
                <SectionCard class="tenant-service-call-success-rate" title="租户服务调用成功率">
                    <CommonEchart
                        :targetData="serviceCallSuccessRateData"
                        refName="serviceCallSuccessRate"
                    ></CommonEchart>
                </SectionCard>
            </div>
            <div class="main-right">
                <SectionCard title="MCP资产情况概览">
                    <div class="mcp-asset-group">
                        <MetricCard v-for="item in mcpAssetCards" :key="item.title" v-bind="item" />
                    </div>
                </SectionCard>
                <SectionCard
                    class="service-ranking-table"
                    title="服务调用情况排行榜"
                    v-model="serviceUsageRadioValue"
                    :radioGroup="serviceUsageRadioList"
                >
                    <DataTable
                        :columns="serviceUsageColumns"
                        :data="serviceUsageTableData"
                        :total="total"
                        :layout="'total, prev, pager, next, sizes, jumper'"
                        :pagination="pagination"
                        :updateTable="getTableData"
                        :showPagination="false"
                    >
                        <template #no="{ row }">
                            <div
                                v-if="row.no < 4"
                                class="service-ranking-table-index"
                                :class="`rank-circle-${row.no}`"
                            ></div>
                            <div v-else class="service-ranking-table-index">{{ row.no }}</div>
                        </template>
                    </DataTable>
                </SectionCard>
            </div>
        </main>
    </div>
</template>
<script>
import SectionCard from '@/script/components/home/<USER>';
import MetricCard from '@/script/components/home/<USER>';
import CommonEchart from '@/script/components/charts/commonEchart.vue';
import DataTable from '@/script/components/tables/DataTable.vue';
import dayjs from 'dayjs';
import Home from '@/script/api/module/home';

export default {
    name: 'HomePage',
    components: {
        SectionCard,
        MetricCard,
        CommonEchart,
        DataTable
    },
    data() {
        return {
            radioValue: 'today',
            dateValue: [],
            radioList: [
                { label: '今日', value: 'today' },
                { label: '昨日', value: 'yesterday' },
                { label: '本周', value: 'week' },
                { label: '本月', value: 'month' },
                { label: '自定义', value: 'custom' }
            ],
            // MCP服务使用情况
            mcpUsageCards: [
                {
                    title: '租户（个）',
                    value: '-',
                    icon: require('../../../img/homePage/card-user.png'),
                    field: 'tenantNum'
                },
                {
                    title: '调用次数',
                    value: '-',
                    icon: require('../../../img/homePage/card-times.png'),
                    field: 'totalCalls'
                },
                {
                    title: '平均调用成功率',
                    value: '-',
                    icon: require('../../../img/homePage/card-rate.png'),
                    field: 'successRate'
                },
                {
                    title: '平均调用耗时（ms）',
                    value: '-',
                    icon: require('../../../img/homePage/card-time.png'),
                    field: 'avgResponseTime'
                }
            ],
            // 租户服务调用次数
            serviceCallTimesData: {
                title: '每分钟记录总量',
                xAxisData: [],
                yAxisName: '次数',
                alignWithLabel: false,
                lineData: [
                    {
                        type: 'bar',
                        data: [],
                        unit: '次',
                        lineType: 'solid',
                        color: '#1565FF'
                    }
                ]
            },
            // 租户服务调用成功率
            serviceCallSuccessRateData: {
                title: '每分钟记录总量',
                xAxisData: [],
                yAxisName: '成功率',
                alignWithLabel: true,
                lineData: [
                    {
                        type: 'line',
                        data: [],
                        unit: '%',
                        isRate: true,
                        lineType: 'solid',
                        color: '#1565FF'
                    }
                ]
            },
            // MCP资产情况概览
            mcpAssetCards: [
                {
                    title: 'MCP服务（个）',
                    value: '-',
                    icon: require('../../../img/homePage/card-service.png'),
                    field: 'mcpNum'
                },
                {
                    title: 'MCP服务资源（个）',
                    value: '-',
                    icon: require('../../../img/homePage/card-resource.png'),
                    field: 'resourceNum'
                },
                {
                    title: 'MCP服务工具（个）',
                    value: '-',
                    icon: require('../../../img/homePage/card-tools.png'),
                    field: 'toolNum'
                }
            ],
            // 服务调用情况排行榜
            serviceUsageRadioValue: 0,
            serviceUsageRadioList: [
                { label: '服务调用', value: 0 },
                { label: '工具调用', value: 1 },
                { label: '资源调用', value: 2 }
            ],
            serviceUsageColumns: [
                { label: '排名', prop: 'no' },
                { label: '服务名称', prop: 'name', 'show-overflow-tooltip': true },
                { label: '调用次数', prop: 'totalCalls' },
                { label: '平均调用耗时(ms)', prop: 'avgResponseTime' },
                { label: '调用成功率', prop: 'successRate' }
            ],
            serviceUsageTableData: [],
            total: 0,
            pagination: {
                curPage: 1,
                pageSize: 10
            }
        };
    },
    watch: {
        radioValue: {
            handler(val) {
                if (val === 'custom') {
                    return;
                }
                const dateMap = {
                    today: [
                        dayjs().format('YYYY-MM-DD 00:00:00'),
                        dayjs().format('YYYY-MM-DD 23:59:59')
                    ],
                    yesterday: [
                        dayjs().subtract(1, 'day').format('YYYY-MM-DD 00:00:00'),
                        dayjs().subtract(1, 'day').format('YYYY-MM-DD 23:59:59')
                    ],
                    week: [
                        dayjs().startOf('week').format('YYYY-MM-DD 00:00:00'),
                        dayjs().endOf('week').format('YYYY-MM-DD 23:59:59')
                    ],
                    month: [
                        dayjs().startOf('month').format('YYYY-MM-DD 00:00:00'),
                        dayjs().endOf('month').format('YYYY-MM-DD 23:59:59')
                    ]
                };
                this.dateValue = dateMap[val];
                this.getHomeData(this.dateValue);
            },
            immediate: true
        },
        serviceUsageRadioValue: {
            handler(val) {
                this.getTableData({});
            }
        }
    },
    methods: {
        handleDateChange(val) {
            if (!val) {
                return;
            }
            val = [
                dayjs(val[0]).format('YYYY-MM-DD 00:00:00'),
                dayjs(val[1]).format('YYYY-MM-DD 23:59:59')
            ];
            this.getHomeData(val);
        },
        getHomeData(date) {
            const [startTime, endTime] = date;
            Home.getTenantStats({
                startTime,
                endTime
            }).then((res) => {
                if (res.serviceFlag === 'TRUE' && res.data) {
                    this.mcpUsageCards = this.mcpUsageCards.map((card) => {
                        const value = res.data[card.field];
                        if (card.field === 'totalCalls' && value > 10 ** 4) {
                            return {
                                ...card,
                                title: '调用次数（万）',
                                value: (value / 10 ** 4).toFixed(2)
                            };
                        }
                        if (card.field === 'successRate') {
                            return {
                                ...card,
                                value: value + '%'
                            };
                        }
                        return {
                            ...card,
                            value
                        };
                    });
                } else {
                    this.mcpUsageCards = this.$options.mcpUsageCards;
                }
            });

            Home.getMCPAssetInfo().then((res) => {
                if (res.serviceFlag === 'TRUE' && res.data) {
                    this.mcpAssetCards = this.mcpAssetCards.map((card) => {
                        const value = res.data[card.field];
                        return {
                            ...card,
                            value
                        };
                    });
                }
            });

            Home.getTenantCallInfo({
                startTime,
                endTime
            }).then((res) => {
                if (res.serviceFlag === 'TRUE' && res.data) {
                    // 按调用次数降序排序
                    res.data.sort((a, b) => b.totalCalls - a.totalCalls);
                    const data = res.data;
                    const chartData = {
                        tenantNames: [],
                        callTimes: [],
                        successRates: []
                    };

                    data.forEach((item) => {
                        chartData.tenantNames.push(item.tenantName);
                        chartData.callTimes.push(item.totalCalls);
                        chartData.successRates.push(item.successRate);
                    });

                    this.serviceCallTimesData = {
                        ...this.serviceCallTimesData,
                        xAxisData: chartData.tenantNames,
                        lineData: [
                            {
                                ...this.serviceCallTimesData.lineData[0],
                                data: chartData.callTimes
                            }
                        ]
                    };

                    this.serviceCallSuccessRateData = {
                        ...this.serviceCallSuccessRateData,
                        xAxisData: chartData.tenantNames,
                        lineData: [
                            {
                                ...this.serviceCallSuccessRateData.lineData[0],
                                data: chartData.successRates
                            }
                        ]
                    };
                } else {
                    this.serviceCallTimesData = this.$options.serviceCallTimesData;
                    this.serviceCallSuccessRateData = this.$options.serviceCallSuccessRateData;
                }
            });
            this.getTableData({});
        },
        getTableData({ curPage = 1, pageSize = 10 }) {
            const dateRange = this.dateValue;
            if (!dateRange || !dateRange.length) return;

            const [startTime, endTime] = dateRange;

            Home.getCallRanking({
                type: this.serviceUsageRadioValue,
                topN: pageSize,
                startTime,
                endTime
            }).then((res) => {
                if (res.serviceFlag === 'TRUE' && res.data) {
                    // 按no排序
                    res.data.sort((a, b) => a.no - b.no);
                    this.serviceUsageTableData = res.data.map((item) => {
                        return {
                            ...item,
                            successRate: item.successRate + '%'
                        };
                    });
                    this.total = res.data.length;
                } else {
                    this.serviceUsageTableData = [];
                    this.total = 0;
                }
            });
        }
    }
};
</script>
<style scoped lang="less">
.home-container {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    gap: 1rem;
    &-header {
        height: 1.25rem;
        margin-left: 0.5rem;
        font-family:
            PingFangSC,
            PingFang SC;
        font-weight: 600;
        font-size: 1rem;
        color: rgba(0, 0, 0, 0.85);
        line-height: 1.25rem;
    }
    &-search {
        height: 3.5rem;
        background: rgba(255, 255, 255, 0.96);
        border-radius: 0.375rem;
        display: flex;
        align-items: center;
        gap: 1rem;
        padding: 0 1rem;
        &-radio {
            height: 2rem;
            /deep/.el-radio-button {
                margin: 0;
                height: 100%;
                &.is-active {
                    .el-radio-button__inner {
                        font-weight: 500;
                        color: #ffffff;
                        background: #1565ff;
                        border-radius: 0.25rem;
                    }
                }
                &__inner {
                    font-family:
                        PingFangSC,
                        PingFang SC;
                    font-weight: 400;
                    font-size: 0.875rem;
                    color: rgba(0, 0, 0, 0.65);

                    line-height: 1.25rem;
                    padding: 0 1.25rem;
                    height: 100%;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                }
            }
        }
        &-date {
            height: 2rem;
            /deep/.el-input__icon {
                display: flex;
                justify-content: center;
                align-items: center;
            }
            /deep/.el-range-separator {
                display: flex;
                justify-content: center;
                align-items: center;
            }
        }
    }
    &-main {
        height: 0;
        flex: 1;
        display: flex;
        gap: 1rem;
        .main-left {
            display: flex;
            flex-direction: column;
            gap: 1rem;
            .mcp-usage-card-group {
                display: flex;
                gap: 0.5rem;
                user-select: none;
            }
            .tenant-service-call-times,
            .tenant-service-call-success-rate {
                flex: 1;
                min-height: 0;
            }
        }
        .main-right {
            width: 0;
            flex: 1;
            display: flex;
            flex-direction: column;
            gap: 1rem;
            .mcp-asset-group {
                display: flex;
                gap: 0.5rem;
                user-select: none;
            }
            .service-ranking-table {
                flex: 1;
                min-height: 0;
                &-index {
                    user-select: none;
                    min-width: 1.5rem;
                    width: fit-content;
                    height: 1.5rem;
                    display: flex;
                    justify-content: center;
                    align-items: center;
                    border-radius: 50%;
                    background: rgba(0, 149, 255, 0.2);
                    background-size: 100% 100%;
                    background-repeat: no-repeat;
                    background-position: center;
                }
                .rank-circle-1 {
                    background-image: url('../../../img/common/gold-medal.png');
                }
                .rank-circle-2 {
                    background-image: url('../../../img/common/silver-medal.png');
                }
                .rank-circle-3 {
                    background-image: url('../../../img/common/bronze-medal.png');
                }
            }
        }
    }
}
</style>
