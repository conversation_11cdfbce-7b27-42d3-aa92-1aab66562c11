<template>
    <div class="permission">
        <div class="permission-container" v-show="!showDetail">
            <header class="permission-container-header">权限配置管理</header>
            <div class="permission-container-search">
                <SearchBar :form-cols="searchFormCols" :form="searchForm">
                    <el-button type="primary" @click="handleSearch">查询</el-button>
                    <el-button style="margin-left: auto" type="plain" icon="el-icon-download"
                        >批量导入
                    </el-button>
                    <el-button
                        type="primary"
                        icon="el-icon-plus"
                        @click="addTenantDialogVisible = true"
                        >新增租户</el-button
                    >
                </SearchBar>
            </div>
            <main class="permission-container-main">
                <CardTable
                    v-loading="isLoading"
                    :data="tenantTableData"
                    :total="total"
                    :layout="'total, prev, pager, next, sizes, jumper'"
                    :pagination="pagination"
                    :updateTable="getTableData"
                    :cardConfig="cardConfig"
                    @cardEvent="handleCardEvent"
                >
                    <template #diyContent="{ cardData }">
                        <div class="diy-content">
                            <div
                                class="diy-content-item"
                                v-for="(value, key) in tenantCardData(cardData.rawData)"
                                :key="key"
                            >
                                <p>
                                    <span class="diy-content-item-key">{{ key }}：</span>
                                    <span
                                        class="diy-content-item-value text-ellipsis"
                                        :title="value"
                                        >{{ value }}
                                    </span>
                                </p>
                            </div>
                        </div>
                    </template>
                </CardTable>
            </main>
        </div>
        <!-- 新增/编辑租户弹窗 -->
        <el-dialog
            :title="isEdit ? '编辑租户' : '新增租户'"
            :visible.sync="addTenantDialogVisible"
            width="736px"
            :close-on-click-modal="false"
            destroy-on-close
            @close="
                addTenantForm = $options.data().addTenantForm;
                isEdit = false;
            "
        >
            <SearchBar ref="addTenantFormRef" :form-cols="addTenantFormCols" :form="addTenantForm">
                <template #tenantPwd>
                    <el-input
                        v-model="addTenantForm.tenantPwd"
                        clearable
                        show-password
                        placeholder="请输入"
                    >
                    </el-input>
                    <span
                        class="tenant-pwd-input-copy"
                        @click="handleCopyPwd(addTenantForm.tenantPwd)"
                        >复制</span
                    >
                </template>
                <template #isToolAuth>
                    <el-radio-group class="is-tool-auth" v-model="addTenantForm.isToolAuth">
                        <el-radio :label="1">是</el-radio>
                        <el-radio :label="0">否</el-radio>
                    </el-radio-group>
                </template>
            </SearchBar>
            <div class="dialog-footer">
                <el-button @click="addTenantDialogVisible = false">取消</el-button>
                <el-button type="primary" @click="handleAddTenant">确定</el-button>
            </div>
        </el-dialog>
        <!-- 租户权限配置 -->
        <TenantPermissionConfig
            :visible.sync="tenantPermissionConfigVisible"
            :tenantKey="tenantKey"
        />
        <!-- 调用密钥管理 -->
        <ApiKeyManage :visible.sync="apiKeyManageVisible" :tenantKey="tenantKey" />
        <!-- 租户配置详情 -->
        <TenantConfigDetail v-if="showDetail" :tenantKey="tenantKey" @back="showDetail = false" />
    </div>
</template>

<script>
import SearchBar from '@/script/components/SearchBar.vue';
import CardTable from '@/script/components/cardTable/index.vue';
import TenantPermissionConfig from './TenantPermissionConfig.vue';
import ApiKeyManage from './ApiKeyManage.vue';
import TenantConfigDetail from './TenantConfigDetail.vue';
import Permission from '@/script/api/module/permission';
import { copyText } from '@/script/utils/method';

export default {
    name: 'PermissionPage',
    components: {
        SearchBar,
        CardTable,
        TenantPermissionConfig,
        ApiKeyManage,
        TenantConfigDetail
    },
    data() {
        return {
            isLoading: false,
            isEdit: false,
            showDetail: false,
            tenantKey: '',
            // 搜索表单
            searchFormCols: [
                [
                    {
                        type: 'el-input',
                        prop: 'tenantName',
                        label: '租户名称：',
                        labelWidth: 'max-content',
                        attrs: {
                            placeholder: '请输入',
                            clearable: true
                        },
                        rules: [],
                        span: 4,
                        isDisabled: false,
                        isShow: true,
                        opts: []
                    },
                    {
                        type: 'el-select',
                        prop: 'isValid',
                        label: '状态：',
                        labelWidth: 'max-content',
                        attrs: {
                            placeholder: '请选择',
                            clearable: false
                        },
                        rules: [],
                        span: 4,
                        isDisabled: false,
                        isShow: true,
                        opts: [
                            {
                                label: '全部',
                                value: ''
                            },
                            {
                                label: '起效',
                                value: 1
                            },
                            {
                                label: '不起效',
                                value: 0
                            }
                        ]
                    },
                    {
                        type: 'tool',
                        prop: 'tool',
                        span: 16,
                        isShow: true
                    }
                ]
            ],
            searchForm: {
                tenantName: '',
                isValid: ''
            },
            // 导入弹窗
            dialogVisible: false,
            dialogType: 'excel', // 'excel' 或 'json'
            // 新增租户弹窗
            addTenantDialogVisible: false,
            addTenantFormCols: [
                [
                    {
                        type: 'el-input',
                        prop: 'tenantName',
                        label: '租户名称：',
                        labelWidth: '180px',
                        attrs: {
                            placeholder: '请输入',
                            clearable: true
                        },
                        rules: [{ required: true, message: '必填', trigger: 'blur' }],
                        span: 22,
                        isDisabled: false,
                        isShow: true,
                        opts: []
                    }
                ],
                [
                    {
                        type: 'slot',
                        prop: 'tenantPwd',
                        label: '租户密钥：',
                        labelWidth: '180px',
                        rules: [{ required: true, message: '必填', trigger: 'blur' }],
                        span: 22,
                        isDisabled: false,
                        isShow: true,
                        opts: []
                    }
                ],
                [
                    {
                        type: 'el-input',
                        prop: 'sourceSystemName',
                        label: '租户所属系统名称：',
                        labelWidth: '180px',
                        attrs: {
                            placeholder: '请输入',
                            clearable: true
                        },
                        rules: [{ required: true, message: '必填', trigger: 'blur' }],
                        span: 22,
                        isDisabled: false,
                        isShow: true,
                        opts: []
                    }
                ],
                [
                    {
                        type: 'slot',
                        prop: 'isToolAuth',
                        label: '是否开启工具鉴权：',
                        labelWidth: '180px',
                        rules: [{ required: true, message: '必填', trigger: 'blur' }],
                        span: 22,
                        isDisabled: false,
                        isShow: true,
                        opts: []
                    }
                ],
                [
                    {
                        type: 'el-input',
                        prop: 'comment',
                        label: '其他备注：',
                        labelWidth: '180px',
                        attrs: {
                            placeholder: '请输入',
                            clearable: true
                        },
                        rules: [{ required: false, message: '必填', trigger: 'blur' }],
                        span: 22,
                        isDisabled: false,
                        isShow: true,
                        opts: []
                    }
                ]
            ],
            addTenantForm: {
                tenantName: '',
                tenantPwd: '',
                sourceSystemName: '',
                isToolAuth: 1,
                comment: ''
            },
            // 租户权限配置弹窗
            tenantPermissionConfigVisible: false,
            // 调用密钥管理
            apiKeyManageVisible: false,
            // 租户表格数据
            tenantTableData: [],
            total: 0,
            pagination: {
                curPage: 1,
                pageSize: 10
            },
            // 卡片配置
            cardConfig: {
                module: 'permission',
                icon: {
                    0: require('../../../img/common/permission-inactive.png'),
                    1: require('../../../img/common/permission-active.png')
                },
                allowHover: true,
                expandType: 'link',
                editType: 'page',
                diyContent: true,
                headerHideItem: ['updateTime', 'delete', 'cancel', 'save', 'tag', 'copy']
            },
            tenantCardData: (data) => {
                let isToolAuth = '';
                data.isToolAuth === 1 && (isToolAuth = '是');
                data.isToolAuth === 0 && (isToolAuth = '否');
                return {
                    所属系统名称: data.sourceSystemName,
                    是否开启工具鉴权: isToolAuth,
                    创建时间: data.createdTime,
                    最后修改时间: data.lastUpdateTime
                };
            }
        };
    },
    created() {
        this.getTableData(this.pagination);
    },
    methods: {
        handleSearch() {
            this.pagination.curPage = 1;
            this.getTableData(this.pagination);
        },
        getTableData({ curPage = 1, pageSize = this.pagination.pageSize }) {
            this.isLoading = true;
            Permission.getTenantPage({
                pageNum: curPage,
                pageSize: pageSize,
                tenantName: this.searchForm.tenantName,
                isValid: this.searchForm.isValid
            })
                .then((res) => {
                    if (res.serviceFlag === 'TRUE') {
                        this.tenantTableData = res.data.list
                            .map((item) => {
                                return {
                                    rawData: item,
                                    id: item.tenantKey,
                                    name: item.tenantName,
                                    description: item.mcpDescription,
                                    isValid: item.isValid,
                                    createdTime: item.createdTime,
                                    lastUpdateTime: item.lastUpdateTime
                                };
                            })
                            .sort((a, b) => {
                                return new Date(b.lastUpdateTime) - new Date(a.lastUpdateTime);
                            });
                        this.total = res.data.total;
                    }
                })
                .finally(() => {
                    this.isLoading = false;
                });
        },
        openImportDialog(val) {
            this.dialogType = val;
            this.dialogVisible = true;
        },
        // 导入成功回调
        handleImportSuccess(data) {
            console.log('导入成功:', data);
            // 处理导入结果数据
            this.$message.success('导入成功');
        },
        handleCopyPwd(pwd) {
            copyText(pwd)
                .then((res) => {
                    this.$message.success(res);
                })
                .catch((err) => {
                    this.$message.error(err);
                });
        },
        // 新增/编辑租户
        handleAddTenant() {
            this.$refs.addTenantFormRef.validForm().then((valid) => {
                if (valid) {
                    let func = 'addTenant';
                    if (this.isEdit) {
                        func = 'updateTenant';
                    }
                    Permission[func](this.addTenantForm).then((res) => {
                        if (res.serviceFlag === 'TRUE') {
                            this.$message.success(res.returnMsg || '新增成功');
                            this.addTenantDialogVisible = false;
                            this.handleSearch();
                        } else {
                            this.$message.error(res.returnMsg || '新增失败');
                        }
                    });
                }
            });
        },
        handleCardEvent(event) {
            const eventMap = {
                link: () => {
                    this.tenantKey = event.params.rawData.tenantKey;
                    this.showDetail = true;
                },
                // 卡片编辑
                edit: () => {
                    this.isEdit = true;
                    this.addTenantForm = {
                        tenantKey: event.params.rawData.tenantKey,
                        tenantName: event.params.rawData.tenantName,
                        tenantPwd: event.params.rawData.tenantPwd,
                        sourceSystemName: event.params.rawData.sourceSystemName,
                        isToolAuth: event.params.rawData.isToolAuth,
                        comment: event.params.rawData.comment
                    };
                    this.addTenantDialogVisible = true;
                },
                // 卡片设置
                setting: () => {
                    this.tenantKey = event.params.rawData.tenantKey;
                    this.tenantPermissionConfigVisible = true;
                },
                // 卡片密钥
                tenantKey: () => {
                    this.tenantKey = event.params.rawData.tenantKey;
                    this.apiKeyManageVisible = true;
                },
                // 切换卡片状态（有效/无效）
                valid: () => {
                    Permission.changeTenantValid({
                        tenantKeys: [event.params.id],
                        isValid: +event.params.isValid
                    }).then((res) => {
                        this.handleTenantResponse(res, '切换成功', '切换失败');
                    });
                }
            };

            eventMap[event.type] && eventMap[event.type]();
        },
        handleTenantResponse(res, successMsg, errorMsg) {
            if (res.serviceFlag === 'TRUE') {
                this.$message.success(res.returnMsg || successMsg);
                this.handleSearch();
            } else {
                this.$message.error(res.returnMsg || errorMsg);
            }
        }
    }
};
</script>

<style scoped lang="less">
.permission {
    width: 100%;
    height: 100%;
}
.permission-container {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    gap: 1rem;
    &-header {
        height: 1.25rem;
        margin-left: 0.5rem;
        font-family:
            PingFangSC,
            PingFang SC;
        font-weight: 600;
        font-size: 1rem;
        color: rgba(0, 0, 0, 0.85);
        line-height: 1.25rem;
    }
    &-search {
        height: 3.5rem;
        background: rgba(255, 255, 255, 0.96);
        border-radius: 0.375rem;
        display: flex;
        align-items: center;
        gap: 1rem;
        padding: 0 1rem;
    }
    &-main {
        height: 0;
        flex: 1;
        display: flex;
        gap: 1rem;
        .diy-content {
            padding: 0 1rem 1rem 3rem;
            display: flex;
            align-items: center;
            gap: 1rem;
            &-item {
                width: 100%;
                height: 2.75rem;
                background: #f6f8fa;
                border-radius: 0.375rem;
                border: 0.0625rem solid #ebedf0;
                display: flex;
                align-items: center;
                justify-content: start;
                padding: 0.5rem 1rem;
                p {
                    display: flex;
                    align-items: center;
                    margin: 0;
                    &::before {
                        content: '';
                        display: block;
                        width: 0.375rem;
                        height: 0.375rem;
                        background: #1565ff;
                        border-radius: 50%;
                        margin-right: 0.5rem;
                    }
                    span {
                        font-family:
                            PingFangSC,
                            PingFang SC;
                        font-weight: 400;
                        font-size: 0.875rem;
                        color: rgba(0, 0, 0, 0.85);
                    }
                }
                &-key {
                    width: auto;
                    flex: none;
                }
                &-value {
                    flex: 1;
                }
            }
        }
    }
}
.dialog-footer {
    text-align: right;
    margin-top: 32px;
}
.tenant-pwd-input-copy {
    position: absolute;
    right: -10px;
    top: 0;
    transform: translateX(100%);
    font-family:
        PingFangSC,
        PingFang SC;
    font-weight: 400;
    font-size: 14px;
    color: #1565ff;
    cursor: pointer;
    user-select: none;
    transition: color 0.2s ease;
    &:hover {
        color: #1565ff99;
    }
}
/deep/.el-radio-group.is-tool-auth {
    height: 32px;
    display: flex;
    align-items: center;
    .el-radio {
        margin-bottom: 0;
        &__input.is-checked + .el-radio__label {
            color: #1565ff;
        }
        &__input.is-checked .el-radio__inner {
            background-color: #1565ff;
            border-color: #1565ff;
        }
        &__label {
            font-family:
                PingFangSC,
                PingFang SC;
            font-weight: 400;
            font-size: 14px;
            color: rgba(0, 0, 0, 0.65);
        }
    }
}
</style>
