<template>
    <div class="form-data-info-content">
        <FormDataTable
            v-model="formDataTableData"
            :columns="formDataTableColumns"
            :defaultRowData="formDataTableDefaultRow"
            :outBorder="false"
            @update:modelValue="handleFormDataTableChange"
        />
    </div>
</template>

<script>
import FormDataTable from '@/script/components/tables/FormDataTable.vue';
const paramsTypeOpts = [
    { label: 'string', value: 'string' },
    { label: 'int', value: 'int' },
    { label: 'float', value: 'float' },
    { label: 'boolean', value: 'boolean' },
    { label: 'array', value: 'array' },
    { label: 'object', value: 'object' }
];

export default {
    name: 'ToolFormDataInfo',
    components: {
        FormDataTable
    },
    props: {
        value: {
            type: Object,
            required: true
        },
        isEdit: {
            type: Boolean,
            default: false
        }
    },
    data() {
        return {
            // FormDataTable相关数据
            formDataTableData: [],
            formDataTableColumns: [
                { title: '参数名', prop: 'paramNameEn', type: 'input' },
                {
                    title: '参数类型',
                    prop: 'paramType',
                    type: 'select',
                    options: paramsTypeOpts
                },
                { title: '参数值', prop: 'paramDefaultValue', type: 'input' },
                {
                    title: '内容类型',
                    prop: 'paramContentType',
                    type: 'select',
                    options: [
                        // { label: 'text', value: 'text' },
                        // { label: 'file', value: 'file' }
                    ]
                },
                {
                    title: '描述',
                    prop: 'paramDescription',
                    type: 'input',
                    width: 'calc((100% - 150px) / 3)'
                }
            ],
            formDataTableDefaultRow: {
                paramNameEn: '',
                paramType: '',
                paramDefaultValue: '',
                paramInType: '',
                paramDescription: '',
                isValid: 1
            }
        };
    },
    computed: {},
    created() {},
    watch: {
        value: {
            deep: true,
            handler() {}
        }
    },
    methods: {
        handleFormDataTableChange(newData) {
            this.formDataTableData = newData;
        }
    }
};
</script>

<style lang="less" scoped>
.form-data-info-content {
    display: flex;
    gap: 1rem;
}
</style>
