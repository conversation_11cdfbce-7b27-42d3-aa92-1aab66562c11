<template>
    <el-dialog
        :title="title"
        :visible.sync="dialogVisible"
        width="1080px"
        :close-on-click-modal="false"
        :before-close="handleClose"
        destroy-on-close
        custom-class="api-key-manage"
        @open="fetchData"
    >
        <main class="dialog-main custom-scrollbar">
            <KeyManageTable
                class="key-table"
                v-model="list"
                @on-refresh="handleRefresh"
                @on-delete="handleDelete"
                v-loading="loading"
            />
            <div class="generate-key">
                <el-button type="primary" @click="generateKey(null)">生成新密钥</el-button>
            </div>
        </main>
        <!-- <footer class="dialog-footer" slot="footer">
            <el-button @click="onCancel">取消</el-button>
            <el-button type="primary" @click="onSuccess">确定</el-button>
        </footer> -->
    </el-dialog>
</template>

<script>
import KeyManageTable from './components/KeyManageTable.vue';
import Permission from '@/script/api/module/permission';

export default {
    name: 'ApiKeyManage',
    components: {
        KeyManageTable
    },
    props: {
        // 弹窗标题
        title: {
            type: String,
            default: '调用密钥管理'
        },
        // 是否显示弹窗
        visible: {
            type: Boolean,
            default: false
        },
        // 租户key
        tenantKey: {
            type: String,
            default: ''
        }
    },
    data() {
        return {
            loading: false,
            list: {
                originList: [],
                newList: []
            }
        };
    },
    computed: {
        dialogVisible: {
            get() {
                return this.visible;
            },
            set(val) {
                this.$emit('update:visible', val);
            }
        }
    },
    methods: {
        /* ====== 弹窗操作 ====== */
        handleClose() {
            this.$emit('on-close');
            this.dialogVisible = false;
        },
        onSuccess() {
            this.$emit('on-success');
            this.dialogVisible = false;
        },
        onCancel() {
            this.$emit('on-cancel');
            this.dialogVisible = false;
        },
        fetchData() {
            this.loading = true;
            Permission.getTenantSecretKeyPage({
                tenantKey: this.tenantKey
            })
                .then((res) => {
                    if (res.serviceFlag === 'TRUE') {
                        this.list = {
                            originList: res.data.list || [],
                            newList: []
                        };
                    } else {
                        this.$message.error(res.returnMsg || '获取密钥失败');
                    }
                })
                .finally(() => {
                    this.loading = false;
                });
        },
        handleRefresh(row) {
            Permission.updateSecretKey({
                tenantKey: this.tenantKey,
                refresh: 'false',
                id: row.id,
                authStartTime: row.authStartTime,
                authEndTime: row.authEndTime
            }).then((res) => {
                if (res.serviceFlag === 'TRUE') {
                    this.$message.success(res.returnMsg || '操作成功');
                    this.fetchData();
                } else {
                    this.$message.error(res.returnMsg || '操作失败');
                }
            });
        },
        handleDelete(row) {
            Permission.changeSecretKeyValid({
                ids: [row.id],
                isValid: 0
            }).then((res) => {
                if (res.serviceFlag === 'TRUE') {
                    this.$message.success(res.returnMsg || '操作成功');
                    this.fetchData();
                } else {
                    this.$message.error(res.returnMsg || '操作失败');
                }
            });
        },
        generateKey() {
            let payload = {};
            // 生成新密钥
            const newList = this.list.newList || [];
            if (newList.length === 0) {
                this.$message.warning('未添加新密钥');
                return;
            }

            const invalidItems = newList.filter((item) => !item.authStartTime || !item.authEndTime);
            if (invalidItems.length > 0) {
                this.$message.warning('请选择授权开始时间和结束时间');
                return;
            }

            payload = {
                tenantKey: this.tenantKey,
                authInfo: newList.map((item) => ({
                    authStartTime: item.authStartTime,
                    authEndTime: item.authEndTime
                }))
            };

            Permission.generateTenantSecretKey(payload).then((res) => {
                if (res.serviceFlag === 'TRUE') {
                    this.$message.success(res.returnMsg || '生成成功');
                    this.fetchData();
                } else {
                    this.$message.error(res.returnMsg || '生成失败');
                }
            });
        }
    }
};
</script>

<style lang="less" scoped>
.dialog-main {
    min-height: 0;
    flex: 1;
    background: #ffffff;
    padding: 16px 24px;
    display: flex;
    flex-direction: column;
    .key-table {
        flex: 1;
    }
    .generate-key {
        flex: none;
        margin-top: 16px;
        display: flex;
        justify-content: center;
    }
}
.dialog-footer {
    display: flex;
    justify-content: flex-end;
    align-items: center;
    background-color: #ffffff;
}
</style>
<style lang="less">
.api-key-manage {
    display: flex;
    flex-direction: column;
    height: 625px;
    background: #ffffff;
    .el-dialog__header {
        height: 56px;
    }
    .el-dialog__body {
        padding: 0;
        min-height: 0;
        flex: 1;
        overflow-y: auto;
        display: flex;
        flex-direction: column;
    }
    .el-dialog__footer {
        height: fit-content;
    }
}
</style>
