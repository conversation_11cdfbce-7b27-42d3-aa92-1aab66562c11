<template>
    <div class="header-params-form">
        <el-row v-for="(row, index) in rows" :key="index" class="header-row">
            <div class="header-row-content">
                <el-col :span="5">
                    <el-input
                        v-model="row.paramNameEn"
                        :class="{ duplicate: row.duplicateNameEn }"
                        size="small"
                        placeholder="请输入参数标识，如：Content-Type"
                        clearable
                    />
                </el-col>
                <el-col :span="2">
                    <el-select
                        v-model="row.isRequired"
                        size="small"
                        placeholder="请选择"
                        class="w-100"
                        popper-class="mcpservice-theme"
                    >
                        <el-option label="必填" :value="1" />
                        <el-option label="选填" :value="0" />
                    </el-select>
                </el-col>
                <el-col :span="5">
                    <el-input
                        v-model="row.paramDefaultValue"
                        size="small"
                        placeholder="请输入参数默认值"
                        clearable
                    />
                </el-col>
                <el-col :span="4">
                    <el-input
                        v-model="row.paramName"
                        :class="{ duplicate: row.duplicateName }"
                        size="small"
                        placeholder="请输入参数名称"
                        clearable
                    />
                </el-col>
                <el-col :span="8">
                    <el-input
                        v-model="row.paramDescription"
                        size="small"
                        placeholder="请输入参数描述"
                        clearable
                    />
                </el-col>
            </div>
            <el-popconfirm
                placement="top-end"
                confirm-button-text="确定"
                cancel-button-text="取消"
                hide-icon
                title="确定删除吗？"
                @confirm="deleteLine(index)"
            >
                <i slot="reference" class="delete-icon el-icon-delete" title="删除"></i>
            </el-popconfirm>
        </el-row>
    </div>
</template>

<script>
export default {
    name: 'HeaderParamsForm',
    props: {
        value: {
            type: Array,
            default: () => []
        }
    },
    data() {
        return {
            nextId: 1,
            internalRows: [],
            suppressWatch: false
        };
    },
    created() {
        this.initRows();
    },
    watch: {
        value: {
            handler: 'initRows',
            immediate: true
        },
        internalRows: {
            deep: true,
            handler() {
                if (this.suppressWatch) {
                    this.suppressWatch = false;
                    return;
                }

                this.suppressWatch = true;

                this.updateParamIds();
                this.updateDuplicates();
                const validData = this.internalRows.filter(
                    (row) => row.paramName && row.paramName.trim()
                );
                this.$emit('input', validData);

                this.$nextTick(() => {
                    this.suppressWatch = false;
                });
            }
        }
    },
    computed: {
        rows: {
            get() {
                return this.internalRows;
            },
            set(val) {
                this.internalRows = val;
            }
        }
    },
    methods: {
        initRows() {
            if (this.suppressWatch) {
                this.suppressWatch = false;
                return;
            }
            if (Array.isArray(this.value) && this.value.length > 0) {
                this.updateNextId(this.value);
                this.internalRows = [...this.value];
            } else {
                this.nextId = 1;
                this.internalRows = [this.createEmptyRow()];
            }
            this.updateParamIds();
        },
        updateNextId(rows) {
            if (Array.isArray(rows) && rows.length > 0) {
                const maxId = Math.max(...rows.map((row) => row.paramId || 0));
                this.nextId = maxId + 1;
            }
        },
        createEmptyRow() {
            const row = {
                paramId: this.nextId++,
                paramDefaultValue: '',
                paramNameEn: '',
                isRequired: 1,
                paramName: '',
                paramDescription: ''
            };
            return row;
        },
        addLine() {
            const newRows = [...this.internalRows];
            newRows.push(this.createEmptyRow());
            this.rows = newRows;
        },
        deleteLine(index) {
            let newRows = [...this.internalRows];
            newRows.splice(index, 1);

            // 如果删除后没有行了，添加一个空行用于显示
            if (newRows.length === 0) {
                this.nextId = 1;
                newRows = [this.createEmptyRow()];
            }

            this.rows = newRows;
        },
        // 根据当前有效行重新编号 paramId
        updateParamIds() {
            let id = 1;
            this.internalRows.forEach((row) => {
                if (row.paramName && row.paramName.trim()) {
                    row.paramId = id;
                    id += 1;
                } else {
                    row.paramId = null;
                }
            });
        },
        /**
         * 标记重复的 paramName 和 paramNameEn
         */
        updateDuplicates() {
            const nameSet = new Set();
            const nameEnSet = new Set();
            this.internalRows.forEach((row) => {
                // 初始化标记
                row.duplicateName = false;
                row.duplicateNameEn = false;
            });

            // 遍历两次以保证只有后续重复项被标记
            this.internalRows.forEach((row) => {
                const name = row.paramName && row.paramName.trim();
                if (name) {
                    if (nameSet.has(name)) {
                        row.duplicateName = true;
                    } else {
                        nameSet.add(name);
                    }
                }

                const nameEn = row.paramNameEn && row.paramNameEn.trim();
                if (nameEn) {
                    if (nameEnSet.has(nameEn)) {
                        row.duplicateNameEn = true;
                    } else {
                        nameEnSet.add(nameEn);
                    }
                }
            });
        }
    }
};
</script>

<style lang="less" scoped>
.header-params-form {
    width: 100%;
    display: flex;
    flex-direction: column;
    gap: 1rem;
    .header-row {
        position: relative;
        width: 100%;
        &-content {
            width: 100%;
            display: flex;
            gap: 1rem;
        }
        .delete-icon {
            position: absolute;
            right: -1.5rem;
            top: 50%;
            transform: translateY(-50%);
            font-size: 1rem;
            color: #f56c6c;
            cursor: pointer;
        }
    }
}

.duplicate {
    position: relative;
    &::after {
        content: '已有相同名称或标识';
        position: absolute;
        left: 0;
        bottom: -12px;
        font-size: 10px;
        line-height: 10px;
        color: #f56c6c;
    }
}
</style>
