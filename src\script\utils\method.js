/**
 * 文本点击复制
 * @param {*} text 需要复制的文本
 * @returns 
 */
export function copyText(text) {
    text = text.trim();
    return new Promise((resolve, reject) => {
        if (navigator.clipboard) {
            navigator.clipboard
                .writeText(text)
                .then(() => resolve('复制成功'))
                .catch((err) => reject('复制失败，请手动复制'));
        }
        else {
            try {
                const textarea = document.createElement('textarea');
                textarea.value = text;
                textarea.style.position = 'fixed';
                document.body.appendChild(textarea);
                textarea.select();

                const success = document.execCommand('copy');
                document.body.removeChild(textarea);

                if (success) {
                    resolve('复制成功');
                } else {
                    reject('复制失败，请手动复制');
                }
            } catch (err) {
                reject('复制失败，请手动复制');
            }
        }
    });
}

/**
 * 删除确认弹窗
 * @param {Object} context - Vue 实例 (this)
 * @param {Object} messageInfo - 提示信息 { title, desc }
 * @param {Function} callback - 用户点击"确定"后的回调
 * @param {Function} cancelCallback - 用户点击"取消"后的回调
 */
export const confirmDelete = (context, messageInfo, callback, cancelCallback) => {
    const h = context.$createElement;

    context.$confirm('', {
        message: h('div', { style: 'display: flex; flex-direction: column; gap: 8px;' }, [
            h('div', { style: 'display: flex; align-items: center; gap: 8px;' }, [
                h('i', {
                    class: 'el-icon-warning',
                    style: 'color:#f90;font-size:24px;line-height:24px;'
                }),
                h('span', { class: 'mcpservice-theme confirm-title' }, messageInfo.title)
            ]),
            h('p', { class: 'mcpservice-theme confirm-desc', style: 'margin: 0;' }, messageInfo.desc)
        ]),
        confirmButtonText: '确定',
        cancelButtonText: '取消'
    }).then(() => {
        callback && callback();
    }).catch(() => {
        cancelCallback && cancelCallback();
    });
};

/**
 * 平滑滚动到指定元素
 * @param {string} elementId 元素ID
 * @param {number} offset 偏移量
 * @param {string} unit 单位
 */
export function smoothScrollTo(elementId, offset = 0, unit = 'px') {
    const element = document.getElementById(elementId);

    // 现代浏览器支持的方式
    if ('scrollBehavior' in document.documentElement.style) {
        element.style.scrollMarginTop = `${offset}${unit}`;
        element.scrollIntoView({ behavior: 'smooth' });
    }
    // 旧浏览器回退方案
    else {
        window.scrollTo({
            top: element.offsetTop - offset,
            behavior: 'smooth'
        });
    }
}

/**
 * 滚动监听（scroll spy）
 * @param {Element} container 滚动容器元素
 * @param {string} sectionSelector 需要监听的 section 选择器，默认 'section'
 * @param {Function} callback 回调 (activeId:string) => void
 * @param {number} threshold 触顶或触底阈值，单位 px
 * @returns {Function} 清理函数，调用后移除监听
 */
export function scrollSpy(container, sectionSelector = 'section', callback, threshold = 24) {
    if (!container || typeof container.addEventListener !== 'function') {
        // eslint-disable-next-line no-console
        console.warn('[scrollSpy] invalid container');
        return () => { };
    }
    let lastActiveId = '';
    const handler = () => {
        const sections = Array.from(container.querySelectorAll(sectionSelector));
        if (!sections.length) return;

        // 默认活跃第一个
        let activeId = sections[0].id;
        const containerRect = container.getBoundingClientRect();
        for (let i = 0; i < sections.length; i += 1) {
            const rect = sections[i].getBoundingClientRect();
            const offsetTop = rect.top - containerRect.top;
            if (offsetTop <= threshold) {
                activeId = sections[i].id;
            } else {
                break;
            }
        }
        // 触底：滚动到最底部时使用最后一个 section
        if (container.scrollHeight - container.scrollTop - container.clientHeight <= threshold) {
            activeId = sections[sections.length - 1].id;
        }
        if (activeId !== lastActiveId) {
            lastActiveId = activeId;
            callback && callback(activeId);
        }
    };

    container.addEventListener('scroll', handler);
    // 初始执行一次
    handler();
    // 返回清理函数
    return () => container.removeEventListener('scroll', handler);
}

/**
 * 将参数数组中的 paramJsonPath 字段解析为 el-tree 可用的数据结构
 * @param {Array} params 参数数组
 * @param {string} prefix 前缀
 * @returns {Array} el-tree data
 */
export function buildTreeFromParams(params = [], prefix = '') {
    if (prefix) {
        prefix = `${prefix}.`;
    }
    const root = [];

    params.forEach((param) => {
        if (!param || !param.paramJsonPath) return;

        // 去掉开头的"$."，并按"."拆分为层级片段
        // 对于数组类型，需要去掉开头的"[*]"
        const segments = String(param.paramJsonPath).replace(/^\$\./, '').split('.').filter(Boolean);
        let currentLevel = root;
        let pathAccum = '';

        segments.forEach((segment, index) => {
            if (segment.endsWith('[*]')) {
                segment = segment.replace(/\[\*\]$/, '');
            }
            if (pathAccum) {
                pathAccum = `${pathAccum}.${segment}`;
            } else {
                pathAccum = segment;
            }

            // 在当前层级查找是否已有该节点
            let node = currentLevel.find((item) => item.label === segment);
            if (!node) {
                node = {
                    label: segment,
                    id: `${prefix}${pathAccum}`,
                    jsonPath: pathAccum,
                    rawJsonPath: param.paramJsonPath,
                    value: param.paramDefaultValue || '',
                    type: param.paramType || '',
                    children: []
                };
                currentLevel.push(node);
            } else {
                // 若节点已存在，但当前遍历到的参数是其父级且类型为 object/array，则提升节点类型
                if (
                    index === segments.length - 1 &&
                    ['object', 'array'].includes(param.paramType) &&
                    !['object', 'array'].includes(node.type)
                ) {
                    node.type = param.paramType;
                    node.value = param.paramDefaultValue || '';
                }
            }

            // 叶子节点保存关联的参数对象，便于后续查找
            if (index === segments.length - 1) {
                if (!node.relatedParams) {
                    node.relatedParams = [];
                }
                node.relatedParams.push(param);
                // 为方便节点与表单一一对应，直接保存引用
                node.formData = param;
            }

            // 进入下一层级
            currentLevel = node.children;
        });
    });

    /**
     * 构建完成后，根据节点是否含有 children 来标记相关参数对象的 isLeafNode
     */
    function markLeaf(nodes = []) {
        nodes.forEach((n) => {
            const isLeaf = !(n.children && n.children.length);
            if (n.relatedParams && n.relatedParams.length) {
                n.relatedParams.forEach((p) => {
                    p.isLeafNode = +isLeaf;
                });
            }
            if (n.children && n.children.length) {
                markLeaf(n.children);
            }
        });
    }

    markLeaf(root);

    return root;
}

/**
 * 将树形结构重新扁平化为路径数组
 * @param {Array} treeData el-tree 数据
 * @returns {Array} 路径字符串数组，不包含开头的 $.
 */
export function flattenTreeToPaths(treeData = []) {
    const paths = [];

    function dfs(nodes, prefix = '') {
        if (!Array.isArray(nodes)) return;
        nodes.forEach((node) => {
            let currentPath = node.label;
            if (prefix) {
                currentPath = `${prefix}.${node.label}`;
            }
            if (node.children && node.children.length) {
                dfs(node.children, currentPath);
            } else {
                paths.push(currentPath);
            }
        });
    }

    dfs(treeData);
    return paths;
}

/**
 * 将参数列表转换为嵌套的JSON对象
 * @param {Array} params - 参数对象数组
 * @returns {Object} 构建好的嵌套JSON对象
 */
export function convertToJson(params) {
    const result = {};

    // 先处理非叶子节点,确保结构完整
    params
        .filter((p) => p.isLeafNode === 0)
        .forEach((param) => {
            const path = param.paramJsonPath;
            setValueByPath(result, path, {});
        });

    // 再处理叶子节点
    params
        .filter((p) => p.isLeafNode === 1)
        .forEach((param) => {
            const path = param.paramJsonPath;
            const value = convertValue(param.paramDefaultValue, param.paramType);
            setValueByPath(result, path, value);
        });

    return result;
}

// 根据JSONPath设置嵌套值
function setValueByPath(obj, path, value) {
    const segments = path.replace(/^\$\.?/, '').split('.');
    let current = obj;

    for (let i = 0; i < segments.length; i++) {
        const segment = segments[i];
        const isLast = i === segments.length - 1;

        // 处理数组通配符 [*]
        if (segment.endsWith('[*]')) {
            const prop = segment.replace(/\[\*\]$/, '');
            if (!current[prop]) {
                current[prop] = [{}];
            } else if (!Array.isArray(current[prop])) {
                current[prop] = [current[prop]];
            } else if (current[prop].length === 0) {
                current[prop].push({});
            }
            current = current[prop][0];
        }
        // 处理数组索引 [n]
        else if (segment.includes('[')) {
            const [prop, idx] = segment.split(/\[|\]/).filter(Boolean);
            if (!current[prop]) {
                current[prop] = [];
            }
            let arrayIndex = 0;
            if (idx) {
                arrayIndex = parseInt(idx);
            }

            // 确保数组足够长
            while (current[prop].length <= arrayIndex) {
                if (isLast) {
                    current[prop].push(value);
                } else {
                    current[prop].push({});
                }
            }

            current = current[prop][arrayIndex];
        }
        // 处理普通属性
        else {
            if (isLast) {
                current[segment] = value;
            } else {
                if (!current[segment]) {
                    current[segment] = {};
                }
                current = current[segment];
            }
        }
    }
}

/* eslint-disable complexity */
/* eslint-disable no-ternary */
// 类型转换辅助函数
function convertValue(value, type) {
    // 未填写返回处理
    if (value === '') {
        if (type === 'string') return '';
        if (type === 'boolean') return undefined;
        if (type === 'int' || type === 'float' || type === 'double') return undefined;
        if (type === 'array') return undefined;
        if (type === 'object') return undefined;
        return undefined;
    }
    if (value === undefined || value === null) return value;
    // 处理 'undefined'/'null' 字面量
    if (value === 'undefined') {
        return undefined;
    }
    if (value === 'null') {
        return null;
    }

    // 类型转换
    const lowerType = (type || '').toLowerCase();
    switch (lowerType) {
        case 'int':
        case 'integer':
            return Number.isNaN(Number(value)) ? undefined : parseInt(value, 10);
        case 'long':
            return Number.isNaN(Number(value)) ? undefined : parseInt(value, 10);
        case 'float':
        case 'double':
        case 'number':
            return Number.isNaN(Number(value)) ? undefined : Number(value);
        case 'boolean':
            if (typeof value === 'boolean') return value;
            if (value === 'true' || value === '1') return true;
            if (value === 'false' || value === '0') return false;
            return undefined;
        case 'array':
            if (Array.isArray(value)) return value;
            // 尝试解析 JSON 字符串
            const arrParsed = safeJsonParse(value, undefined);
            return Array.isArray(arrParsed) ? arrParsed : undefined;
        case 'object':
            if (typeof value === 'object' && value !== null && !Array.isArray(value)) {
                return value;
            }
            const objParsed = safeJsonParse(value, undefined);
            return typeof objParsed === 'object' && objParsed !== null && !Array.isArray(objParsed)
                ? objParsed
                : undefined;
        default:
            return value;
    }
}

// 安全的JSON解析辅助函数
function safeJsonParse(value, defaultValue) {
    try {
        return JSON.parse(value || JSON.stringify(defaultValue));
    } catch (error) {
        return defaultValue;
    }
}