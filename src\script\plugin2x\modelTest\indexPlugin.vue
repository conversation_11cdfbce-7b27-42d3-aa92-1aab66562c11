<template>
    <div class="model-test-container">
        <header class="model-test-container-header">大模型调用测试</header>
    </div>
</template>

<script>
export default {
    name: 'ModelTestPage'
};
</script>

<style scoped lang="less">
.model-test-container {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    gap: 1rem;
    &-header {
        height: 1.25rem;
        margin-left: 0.5rem;
        font-family:
            PingFangSC,
            PingFang SC;
        font-weight: 600;
        font-size: 1rem;
        color: rgba(0, 0, 0, 0.85);
        line-height: 1.25rem;
    }
}
</style>
