const styleTagId = 'mcpservice-custom-style';

export const styleMixin = {
    mounted() {
        // 设置根字体大小
        // document.documentElement.style.fontSize = 'calc(100vw * 16 / 1920)';
        document.documentElement.style.fontSize = '16px';

        // 动态插入 CSS
        const style = document.createElement('style');
        style.id = styleTagId;
        style.textContent = `
            body, div, li, a, span, label, i, h1, h2, h3, h4, h5, input, pre, code {
                font-family: PingFangSC, PingFang SC;
            }
        `;
        document.head.appendChild(style);
    },
    destroyed() {
        // 移除样式
        document.documentElement.style.fontSize = 'unset';
        const style = document.getElementById(styleTagId);
        if (style) {
            document.head.removeChild(style);
        }
    }
};