<template>
    <div class="params-info-form">
        <el-radio-group
            class="params-info-radio-group"
            size="small"
            v-model="paramsInfoRadioValue"
            @change="handleParamsInfoRadioChange"
        >
            <el-radio-button
                v-for="(item, index) in paramsInfoRadioGroup"
                :key="index"
                :label="item.value"
            >
                {{ item.label }}
            </el-radio-button>
        </el-radio-group>

        <!-- 主体区域：左侧树 + 右侧表单 -->
        <div class="params-info-content">
            <div class="content-left">
                <header class="left-header">
                    <span>参数结构</span>
                </header>
                <main class="left-main custom-scrollbar">
                    <el-tree
                        ref="paramsInfoTree"
                        class="left-el-tree"
                        :data="paramsInfoTreeData"
                        node-key="id"
                        :props="paramsInfoTreeProps"
                        highlight-current
                        :expand-on-click-node="false"
                        :default-expand-all="false"
                        @node-click="handleParamsInfoTreeClick"
                    >
                        <template #default="{ node }">
                            <!-- 自定义节点，hover 时展示操作按钮 -->
                            <div
                                class="node-wrapper"
                                @mouseenter="editNodeId !== node.data.id && (hoverNode = node)"
                                @mouseleave="hoverNode = null"
                            >
                                <span
                                    v-if="editNodeId !== node.data.id"
                                    class="node-label text-ellipsis"
                                    :class="{ 'root-node-icon': node.level === 1 }"
                                    :title="node.label"
                                >
                                    {{ node.label }}
                                </span>
                                <el-input
                                    v-else
                                    v-model.trim="node.data.label"
                                    size="mini"
                                    clearable
                                    class="rename-input"
                                    :ref="'renameInput-' + node.data.id"
                                    @keyup.enter.native="finishRename(node)"
                                    @blur="finishRename(node)"
                                    @click.stop
                                />
                                <span
                                    v-show="hoverNode === node && editNodeId !== node.data.id"
                                    class="node-actions"
                                    @click.stop
                                >
                                    <!-- 新增子节点：根据 paramsInfoRadioValue 和节点类型控制显示 -->
                                    <div
                                        v-if="shouldShowAddButton(node)"
                                        class="operation-action"
                                        title="新增子节点"
                                        @click.stop="addChildNode(node)"
                                    >
                                        <i class="el-icon-plus action-icon" />
                                    </div>
                                    <!-- 非根节点才展示更多操作 -->
                                    <el-popover
                                        v-if="node.level > 1"
                                        :ref="'popover-' + node.data.id"
                                        placement="bottom-end"
                                        width="120"
                                        trigger="click"
                                        @show="handlePopoverShow(node)"
                                        @hide="handlePopoverHide(node)"
                                    >
                                        <div class="popover-action more-btn-wrapper">
                                            <div
                                                class="popover-action-item"
                                                @click.stop="deleteNode(node)"
                                            >
                                                <i class="el-icon-delete" />
                                                <span>删除</span>
                                            </div>
                                            <div
                                                class="popover-action-item"
                                                @click.stop="renameNode(node)"
                                            >
                                                <i class="el-icon-edit" />
                                                <span>重命名</span>
                                            </div>
                                        </div>
                                        <div
                                            slot="reference"
                                            class="operation-action"
                                            title="更多"
                                            @click.stop
                                        >
                                            <i class="el-icon-more action-icon" />
                                        </div>
                                    </el-popover>
                                </span>
                            </div>
                        </template>
                    </el-tree>
                </main>
            </div>
            <div class="content-right">
                <SearchBar
                    v-if="paramsInfoForm.paramNameEn"
                    ref="paramsInfoFormRef"
                    :form-cols="dynamicParamsInfoFormCols"
                    :form="paramsInfoForm"
                />
                <div v-else class="no-params-tip">
                    <span>请选择或新增至少一个参数</span>
                </div>
            </div>
        </div>
        <!-- <div class="params-info-content" v-show="paramsInfoRadioValue === 2">
            <FormDataTable
                v-model="formDataTableData"
                :columns="formDataTableColumns"
                :defaultRowData="formDataTableDefaultRow"
                :outBorder="false"
                @update:modelValue="handleFormDataTableChange"
            />
        </div> -->
    </div>
</template>

<script>
import SearchBar from '@/script/components/SearchBar.vue';
import FormDataTable from '@/script/components/tables/FormDataTable.vue';
import { buildTreeFromParams } from '@/script/utils/method';

const paramsTypeOpts = [
    { label: 'string', value: 'string' },
    { label: 'int', value: 'int' },
    { label: 'float', value: 'float' },
    { label: 'boolean', value: 'boolean' },
    { label: 'array', value: 'array' },
    { label: 'object', value: 'object' }
];

export default {
    name: 'ParamsInfoForm',
    components: { SearchBar, FormDataTable },
    props: {
        value: {
            type: Array,
            default: () => []
        }
    },
    data() {
        return {
            // radio 选择 2-Body@form-data, 3-Body@raw
            paramsInfoRadioValue: 3,
            paramsInfoRadioGroup: [
                { label: 'raw', value: 3 },
                { label: 'form-data', value: 2 }
            ],
            // 树结构数据
            paramsInfoTreeData: [
                { id: 'in-params', label: '入参信息', children: [] },
                { id: 'out-params', label: '出参信息', children: [] }
            ],
            paramsInfoTreeProps: { children: 'children', label: 'label' },
            // 右侧表单
            paramsInfoFormCols: [
                [
                    {
                        type: 'el-input',
                        prop: 'paramNameEn',
                        label: '参数标识：',
                        labelWidth: '125px',
                        attrs: { placeholder: '请输入', clearable: true, disabled: true },
                        rules: [{ required: true, message: '必填', trigger: 'blur' }],
                        span: 12,
                        isDisabled: false,
                        isShow: true,
                        opts: []
                    },
                    {
                        type: 'el-select',
                        prop: 'paramInout',
                        label: '参数类别：',
                        labelWidth: '160px',
                        attrs: { placeholder: '请输入', clearable: true, disabled: true },
                        rules: [{ required: true, message: '必填', trigger: 'change' }],
                        span: 12,
                        isDisabled: false,
                        isShow: true,
                        opts: [
                            { label: 'IN', value: 'IN' },
                            { label: 'OUT', value: 'OUT' }
                        ]
                    }
                ],
                [
                    {
                        type: 'el-input',
                        prop: 'paramName',
                        label: '参数名称：',
                        labelWidth: '125px',
                        attrs: { placeholder: '请输入', clearable: true },
                        rules: [{ required: true, message: '必填', trigger: 'blur' }],
                        span: 12,
                        isDisabled: false,
                        isShow: true,
                        opts: []
                    },
                    {
                        type: 'el-select',
                        prop: 'paramType',
                        label: '参数类型：',
                        labelWidth: '160px',
                        attrs: {
                            placeholder: '请输入',
                            clearable: true,
                            disabled: false
                        },
                        listeners: {
                            change: (val) => {
                                this.handleParamTypeChange(val);
                            }
                        },
                        rules: [{ required: true, message: '必填', trigger: 'change' }],
                        span: 12,
                        isDisabled: false,
                        isShow: true,
                        opts: paramsTypeOpts
                    }
                ],
                [
                    {
                        type: 'el-input',
                        prop: 'paramDefaultValue',
                        label: '参数默认值：',
                        labelWidth: '125px',
                        attrs: {
                            placeholder: '请输入',
                            clearable: true,
                            disabled: false
                        },
                        rules: [
                            { required: false, trigger: 'blur' },
                            {
                                validator: (rule, value, callback) => {
                                    this.validateParamDefaultValue(value, callback);
                                },
                                trigger: ['blur', 'change']
                            }
                        ],
                        span: 12,
                        isDisabled: false,
                        isShow: true,
                        opts: []
                    },
                    {
                        type: 'el-input',
                        prop: 'displayConditions',
                        label: '显示条件：',
                        labelWidth: '160px',
                        attrs: { placeholder: '请输入', clearable: true },
                        rules: [{ required: false, message: '必填', trigger: 'blur' }],
                        span: 12,
                        isDisabled: false,
                        isShow: true,
                        opts: []
                    }
                ],
                [
                    {
                        type: 'el-input',
                        prop: 'paramDescription',
                        label: '参数描述：',
                        labelWidth: '125px',
                        attrs: { placeholder: '请输入', clearable: true },
                        rules: [{ required: true, message: '必填', trigger: 'blur' }],
                        span: 12,
                        isDisabled: false,
                        isShow: true,
                        opts: []
                    },
                    {
                        type: 'el-input',
                        prop: 'paramJsonPath',
                        label: '参数路径：',
                        labelWidth: '160px',
                        attrs: { placeholder: '根据层级自动生成', clearable: true, readonly: true },
                        rules: [{ required: true, message: '必填', trigger: 'blur' }],
                        span: 12,
                        isDisabled: false,
                        isShow: true,
                        opts: []
                    }
                ],
                [
                    {
                        type: 'el-select',
                        prop: 'isRequired',
                        label: '是否必填：',
                        labelWidth: '125px',
                        attrs: { placeholder: '请选择', clearable: true },
                        rules: [{ required: true, message: '必填', trigger: 'change' }],
                        span: 12,
                        isDisabled: false,
                        isShow: true,
                        opts: [
                            { label: '是', value: 1 },
                            { label: '否', value: 0 }
                        ]
                    },
                    {
                        type: 'el-select',
                        prop: 'isValid',
                        label: '是否起效：',
                        labelWidth: '160px',
                        attrs: { placeholder: '请输入', clearable: true },
                        rules: [{ required: true, message: '必填', trigger: 'change' }],
                        span: 12,
                        isDisabled: false,
                        isShow: true,
                        opts: [
                            { label: '是', value: 1 },
                            { label: '否', value: 0 }
                        ]
                    }
                ],
                [
                    {
                        type: 'el-input',
                        prop: 'paramComment',
                        label: '其他备注：',
                        labelWidth: '125px',
                        attrs: { placeholder: '请输入', clearable: true },
                        rules: [{ required: false, message: '必填', trigger: 'blur' }],
                        span: 12,
                        isDisabled: false,
                        isShow: true,
                        opts: []
                    },
                    {
                        type: 'el-select',
                        prop: 'paramHandleChoice',
                        label: '指定处理输出参数：',
                        labelWidth: '160px',
                        attrs: { placeholder: '请选择', clearable: true },
                        rules: [{ required: true, message: '必填', trigger: 'change' }],
                        span: 12,
                        isDisabled: false,
                        isShow: true,
                        opts: [
                            { label: '是', value: 1 },
                            { label: '否', value: 0 }
                        ]
                    }
                ]
            ],
            paramsInfoForm: {},
            hoverNode: null,
            popoverNodeId: null,
            editNodeId: null,
            renamePrevLabel: '',
            // FormDataTable相关数据
            // formDataTableData: [],
            // formDataTableColumns: [
            //     { title: '参数名', prop: 'paramNameEn', type: 'input' },
            //     {
            //         title: '参数类型',
            //         prop: 'paramType',
            //         type: 'select',
            //         options: paramsTypeOpts
            //     },
            //     { title: '参数值', prop: 'paramDefaultValue', type: 'input' },
            //     {
            //         title: '内容类型',
            //         prop: 'paramContentType',
            //         type: 'select',
            //         options: [
            //             // { label: 'text', value: 'text' },
            //             // { label: 'file', value: 'file' }
            //         ]
            //     },
            //     {
            //         title: '描述',
            //         prop: 'paramDescription',
            //         type: 'input',
            //         width: 'calc((100% - 150px) / 3)'
            //     }
            // ],
            // formDataTableDefaultRow: {
            //     paramNameEn: '',
            //     paramType: '',
            //     paramDefaultValue: '',
            //     paramInType: '',
            //     paramDescription: '',
            //     isValid: 1
            // },
            currentNodeKey: null,
            currentNode: null,
            oldParamType: null
        };
    },
    computed: {
        model: {
            get() {
                return this.value;
            },
            set(val) {
                val.forEach((item, index) => {
                    this.$set(item, 'paramId', index + 1);
                });
                this.$emit('input', val);
            }
        },
        // 动态计算表单配置，根据当前节点类型和模式控制字段显示
        // 实现智能布局重组：过滤隐藏字段，重新排列表单项，确保布局紧凑
        dynamicParamsInfoFormCols() {
            // 第一步：处理字段显示逻辑，生成带有显示状态的表单项
            const processedCols = this.paramsInfoFormCols.map((row) => {
                return row.map((col) => {
                    const newCol = { ...col };

                    // 控制"指定处理输出参数"字段的显示
                    if (col.prop === 'paramHandleChoice') {
                        // 只有出参信息节点才显示此字段
                        const isOutParam = this.paramsInfoForm.paramInout === 'OUT';
                        newCol.isShow = isOutParam;
                    }

                    // 控制"参数路径"字段的显示
                    if (col.prop === 'paramJsonPath') {
                        // form-data 模式下的入参信息不显示参数路径
                        const isFormDataMode = this.paramsInfoRadioValue === 2;
                        const isInParam = this.paramsInfoForm.paramInout === 'IN';
                        newCol.isShow = !(isFormDataMode && isInParam);
                    }

                    return newCol;
                });
            });

            // 第二步：收集所有可见的表单项
            const visibleCols = [];
            processedCols.forEach((row) => {
                row.forEach((col) => {
                    if (col.isShow !== false) {
                        visibleCols.push({ ...col });
                    }
                });
            });

            // 第三步：重新组织表单项，每行最多两个，确保布局紧凑
            const reorganizedCols = [];
            for (let i = 0; i < visibleCols.length; i += 2) {
                const row = [];

                // 检查这一行是否只有一个表单项
                const hasSecondCol = i + 1 < visibleCols.length;

                // 添加第一个表单项
                if (visibleCols[i]) {
                    const firstCol = { ...visibleCols[i] };
                    // 如果这一行只有一个表单项，设置 span 为 24 占满整行
                    if (!hasSecondCol) {
                        firstCol.span = 24;
                    } else {
                        firstCol.span = 12;
                    }
                    row.push(firstCol);
                }

                // 添加第二个表单项（如果存在）
                if (hasSecondCol && visibleCols[i + 1]) {
                    const secondCol = { ...visibleCols[i + 1] };
                    secondCol.span = 12;
                    row.push(secondCol);
                }

                reorganizedCols.push(row);
            }

            return reorganizedCols;
        }
    },
    watch: {
        paramsInfoRadioValue(newVal) {
            // if (newVal === 2 && this.formDataTableData.length === 0) {
            //     this.initFormDataTableData();
            //     return;
            // }
        },
        'paramsInfoForm.paramType': {
            handler(newVal, oldVal) {
                this.oldParamType = oldVal;
                this.$nextTick(() => {
                    // 如果有子节点且不是对象或数组类型,禁用输入并清空值
                    const curNode = this.currentNode;
                    if (
                        curNode &&
                        curNode.childNodes &&
                        curNode.childNodes.length > 0 &&
                        newVal !== 'object' &&
                        newVal !== 'array'
                    ) {
                        this.disableAndClearDefaultValue();
                    } else {
                        this.restoreDefaultValueInput();
                    }

                    // 触发表单校验
                    if (this.$refs.paramsInfoFormRef) {
                        // 先清除校验结果
                        this.$refs.paramsInfoFormRef.clearValidate(['paramDefaultValue']);
                        // 如果有值,重新触发校验
                        if (this.paramsInfoForm.paramDefaultValue !== undefined) {
                            this.$refs.paramsInfoFormRef.validateField('paramDefaultValue');
                        }
                    }
                });
            }
        },
        'paramsInfoForm.paramDefaultValue': {
            handler(newVal) {
                if (newVal !== undefined) {
                    this.validateParamDefaultValue(newVal);
                }
            }
        }
    },
    methods: {
        handleParamsInfoRadioChange(val) {
            // 如果值没有变化，直接返回
            if (val === this.paramsInfoRadioValue) {
                return;
            }

            // 获取目标模式的标签
            const targetModeItem = this.paramsInfoRadioGroup.find(item => item.value === val);
            let targetMode = '';
            if (targetModeItem) {
                targetMode = targetModeItem.label;
            }

            // 检查入参信息是否有子节点
            const inParamsNode = this.paramsInfoTreeData.find(item => item.id === 'in-params');
            const hasInParamsChildren = inParamsNode && inParamsNode.children && inParamsNode.children.length > 0;

            if (hasInParamsChildren) {
                // 暂存当前值
                const originalValue = this.paramsInfoRadioValue;

                this.$confirm(
                    `切换到 ${targetMode} 模式后，当前入参信息下的所有节点将被清空，且两种模式的数据不可共存。是否确认切换？`,
                    '切换模式确认',
                    {
                        confirmButtonText: '确认切换',
                        cancelButtonText: '取消',
                        type: 'warning'
                    }
                ).then(() => {
                    // 确认切换，清空入参信息下的所有子节点
                    this.clearInParamsChildren();
                    this.handleModeSwitch(val);
                }).catch(() => {
                    // 取消切换，恢复原值
                    this.$nextTick(() => {
                        this.paramsInfoRadioValue = originalValue;
                    });
                });
            } else {
                // 没有子节点，直接切换
                this.handleModeSwitch(val);
            }
        },

        /**
         * 清空入参信息下的所有子节点
         */
        clearInParamsChildren() {
            const inParamsNode = this.paramsInfoTreeData.find(item => item.id === 'in-params');
            if (inParamsNode) {
                // 清空子节点数组
                inParamsNode.children = [];

                // 清空相关的表单数据
                this.model = this.model.filter(item => item.paramInout !== 'IN');

                // 清空当前表单
                this.paramsInfoForm = {};
                this.currentNodeKey = null;
                this.currentNode = null;
            }
        },

        /**
         * 处理模式切换后的逻辑
         */
        handleModeSwitch(val) {
            if (val === 2 && this.formDataTableData.length === 0) {
                this.initFormDataTableData();
            }
        },
        // 校验整数类型
        validateIntType(value) {
            if (value === 'undefined') return null;
            if (!/^-?\d+$/.test(value)) {
                return '请输入有效的整数';
            }
            // 检查是否在安全范围内
            const num = parseInt(value);
            if (num > Number.MAX_SAFE_INTEGER || num < Number.MIN_SAFE_INTEGER) {
                return '数值超出安全范围';
            }
            return null;
        },

        // 校验浮点数类型
        validateFloatType(value) {
            if (value === 'undefined') return null;
            if (!/^-?\d*\.?\d+$/.test(value)) {
                return '请输入有效的数字';
            }
            // 检查总位数是否超过15位
            const digits = value.replace(/[.-]/g, '').length;
            if (digits > 15) {
                return '数值位数不能超过15位';
            }
            return null;
        },

        // 校验布尔类型
        validateBooleanType(value) {
            return null;
        },

        // 校验数组类型
        validateArrayType(value) {
            if (['undefined', 'null'].includes(value)) return null;
            try {
                const arr = JSON.parse(value);
                if (!Array.isArray(arr)) {
                    return '请输入有效的数组格式';
                }
                return null;
            } catch (e) {
                return '请输入有效的数组格式';
            }
        },

        // 校验对象类型
        validateObjectType(value) {
            if (['undefined', 'null'].includes(value)) return null;
            try {
                const obj = JSON.parse(value);
                if (typeof obj !== 'object' || Array.isArray(obj)) {
                    return '请输入有效的对象格式';
                }
                return null;
            } catch (e) {
                return '请输入有效的对象格式';
            }
        },

        // 参数默认值校验主方法
        validateParamDefaultValue(value, callback) {
            // 如果没有值,直接通过(非必填)
            if (value === undefined || value === '') {
                callback && callback();
                return;
            }

            const type = this.paramsInfoForm.paramType;
            let error = null;

            // 根据类型调用对应的校验方法
            const validatorMap = {
                int: this.validateIntType,
                float: this.validateFloatType,
                boolean: this.validateBooleanType,
                array: this.validateArrayType,
                object: this.validateObjectType
            };

            if (validatorMap[type]) {
                error = validatorMap[type](value);
            }

            if (error) {
                callback && callback(new Error(error));
            } else {
                callback && callback();
            }
        },

        // 禁用输入框并清空值
        disableAndClearDefaultValue() {
            const type = this.paramsInfoForm.paramType;
            // 对象和数组类型不受子节点影响
            if (type === 'object' || type === 'array') {
                return;
            }

            // 找到 paramDefaultValue 的配置
            const defaultValueCol = this.paramsInfoFormCols
                .flat()
                .find((col) => col.prop === 'paramDefaultValue');
            if (defaultValueCol) {
                this.$set(defaultValueCol.attrs, 'disabled', true);
                this.$set(defaultValueCol.attrs, 'placeholder', '已有子节点，无需输入');
                // 清空值
                if (this.paramsInfoForm.paramDefaultValue !== undefined) {
                    this.$set(this.paramsInfoForm, 'paramDefaultValue', undefined);
                }
            }
        },

        // 恢复输入框状态
        restoreDefaultValueInput() {
            const defaultValueCol = this.paramsInfoFormCols
                .flat()
                .find((col) => col.prop === 'paramDefaultValue');
            if (defaultValueCol) {
                this.$set(defaultValueCol.attrs, 'disabled', false);
                this.$set(defaultValueCol.attrs, 'placeholder', '请输入');
            }
        },

        // 校验当前节点右侧表单
        async validateCurrentForm() {
            if (this.$refs.paramsInfoFormRef && this.$refs.paramsInfoFormRef.validForm) {
                const res = await this.$refs.paramsInfoFormRef.validForm();
                return res === true;
            }
            return true; // 没有表单直接视为通过
        },

        // 获取节点 paramType
        getNodeParamType(node) {
            if (!node || !node.data) return '';
            if (node.data.formData) return node.data.formData.paramType;
            if (node.data.relatedParams && node.data.relatedParams.length) {
                return node.data.relatedParams[0].paramType;
            }
            return '';
        },
        initData() {
            // raw
            const inParams = this.value.filter((item) => item.paramInout === 'IN');
            const outParams = this.value.filter((item) => item.paramInout === 'OUT');
            this.paramsInfoTreeData[0].children = buildTreeFromParams(
                inParams,
                this.paramsInfoTreeData[0].id
            );
            this.paramsInfoTreeData[1].children = buildTreeFromParams(
                outParams,
                this.paramsInfoTreeData[1].id
            );
            this.$nextTick(() => {
                if (this.paramsInfoTreeData[0].children.length > 0) {
                    const firstNode = this.$refs.paramsInfoTree.getNode(
                        this.paramsInfoTreeData[0].children[0].id
                    );

                    this.handleParamsInfoTreeClick(firstNode.data, firstNode);
                }
            });
            // form-data
            // this.formDataTableData = this.value.map((item) => {
            //     return {
            //         paramNameEn: item.paramNameEn,
            //         paramType: item.paramType,
            //         paramDefaultValue: item.paramDefaultValue,
            //         paramInType: item.paramInType,
            //         paramDescription: item.paramDescription,
            //         isValid: item.isValid
            //     };
            // });
        },
        /**
         * 初始化FormDataTable数据
         */
        // initFormDataTableData() {
        //     if (this.formDataTableData.length === 0) {
        //         this.formDataTableData = [{ ...this.formDataTableDefaultRow }];
        //     }
        // },
        /**
         * 处理FormDataTable数据变化
         */
        // handleFormDataTableChange(newData) {
        //     this.formDataTableData = newData;
        // },
        /**
         * 查找第一级父节点
         */
        findFirstLevelParent(node) {
            if (!node.parent || node.parent.level === 0) {
                return node.data;
            }
            return this.findFirstLevelParent(node.parent);
        },
        handleParamTypeChange(val) {
            this.$nextTick(() => {
                const curNode = this.currentNode;
                if (!curNode) return;

                // 有子节点时只能选择 object/array
                if (curNode.childNodes && curNode.childNodes.length > 0) {
                    if (!['object', 'array'].includes(val)) {
                        this.$message.warning('当前节点包含子节点，只能选择 object 或 array 类型');
                        this.$set(this.paramsInfoForm, 'paramType', this.oldParamType);
                        return;
                    }
                }

                // 处理数组类型的路径更新
                if (val === 'array' || this.oldParamType === 'array') {
                    const pathParts = curNode.data.jsonPath.split('.');
                    const parentPath = pathParts.join('.');
                    curNode.childNodes &&
                        curNode.childNodes.forEach((child) => {
                            this.updateChildrenPath(child, parentPath, '');
                        });
                }

                // 根据类型切换 paramDefaultValue 的组件类型
                const defaultValueCol = this.paramsInfoFormCols
                    .flat()
                    .find((col) => col.prop === 'paramDefaultValue');
                if (defaultValueCol) {
                    if (val === 'boolean') {
                        defaultValueCol.type = 'el-select';
                        defaultValueCol.attrs.placeholder = '请选择';
                        defaultValueCol.opts = [
                            { label: 'true', value: 'true' },
                            { label: 'false', value: 'false' }
                        ];
                        // 如果当前值不是 true/false，清空它
                        if (
                            this.paramsInfoForm.paramDefaultValue !== 'true' &&
                            this.paramsInfoForm.paramDefaultValue !== 'false'
                        ) {
                            this.$set(this.paramsInfoForm, 'paramDefaultValue', undefined);
                        }
                    } else {
                        defaultValueCol.type = 'el-input';
                        defaultValueCol.opts = [];
                    }
                }

                // 触发表单校验
                if (this.$refs.paramsInfoFormRef) {
                    this.$refs.paramsInfoFormRef.clearValidate(['paramDefaultValue']);
                    if (this.paramsInfoForm.paramDefaultValue !== undefined) {
                        this.$refs.paramsInfoFormRef.validateField('paramDefaultValue');
                    }
                }
            });
        },
        /**
         * 树节点点击，先校验当前表单，校验通过后才能切换
         */
        async handleParamsInfoTreeClick(data, node) {
            // 如果当前有节点在编辑状态，阻止点击
            if (this.editNodeId) {
                return;
            }

            if (this.currentNodeKey && node.data.id !== this.currentNodeKey) {
                const ok = await this.validateCurrentForm();
                if (!ok) {
                    this.$message.warning('请先完成当前节点表单');
                    this.$refs.paramsInfoTree.setCurrentKey(this.currentNodeKey);
                    return;
                }
            }

            if (node.level === 1) {
                this.$refs.paramsInfoTree.setCurrentKey(this.currentNodeKey);
                return;
            }

            this.currentNodeKey = node.data.id;
            this.currentNode = node;
            this.$nextTick(() => {
                this.$refs.paramsInfoTree.setCurrentKey(this.currentNodeKey);

                // 如果有子节点，限制参数类型选择
                const typeCol = this.paramsInfoFormCols
                    .flat()
                    .find((col) => col.prop === 'paramType');
                if (typeCol) {
                    if (node.childNodes && node.childNodes.length > 0) {
                        typeCol.opts = paramsTypeOpts.filter((opt) =>
                            ['object', 'array'].includes(opt.value)
                        );
                    } else {
                        typeCol.opts = paramsTypeOpts;
                    }
                }
            });

            this.closeAllPopovers();
            // 绑定表单数据到节点的 formData 保持引用
            if (data.formData) {
                this.paramsInfoForm = data.formData;
            } else {
                const firstLevelParent = this.findFirstLevelParent(node);
                let paramInout = 'IN';
                if (firstLevelParent.id === 'out-params') {
                    paramInout = 'OUT';
                }
                const paramJsonPath = '$.' + data.jsonPath;
                let isLeafNode = 1;
                if (data.children && data.children.length > 0) {
                    isLeafNode = 0;
                }
                this.paramsInfoForm = {
                    paramNameEn: data.label,
                    paramName: '',
                    paramInout,
                    paramType: '',
                    paramDefaultValue: '',
                    paramDescription: '',
                    paramJsonPath,
                    isRequired: 0,
                    paramComment: '',
                    paramHandleChoice: 0,
                    isLeafNode
                };
                this.$set(node.data, 'formData', this.paramsInfoForm);
                this.model = [...this.model, this.paramsInfoForm];
            }
        },
        /** 聚焦重命名输入框 */
        focusRenameInput(id) {
            const inputRef = this.$refs[`renameInput-${id}`];
            if (inputRef && inputRef.focus) {
                inputRef.focus();
            } else if (inputRef && inputRef.$el) {
                const inp = inputRef.$el.querySelector('input');
                inp && inp.focus();
            }
        },
        /**
         * 新增子节点，需满足：
         * 1. 当前节点表单已通过校验；
         * 2. 当前节点 paramType 为 object / array（或根节点）
         */
        async addChildNode(node) {
            const ok = await this.validateCurrentForm();
            if (!ok) {
                return;
            }

            if (node.level > 1) {
                const type = this.getNodeParamType(node);
                if (!['object', 'array'].includes(type)) {
                    this.$message.warning('当节点类型为 object 或 array 时才能新增子节点');
                    return;
                }
            }

            this.closeAllPopovers();
            const newId = `node-${Date.now()}`;
            const newNode = { id: newId, label: '', children: [] };
            if (!node.data.children) {
                this.$set(node.data, 'children', []);
            }
            node.data.children.push(newNode);

            this.editNodeId = newId;
            this.renamePrevLabel = '';

            node.expanded = true;
            setTimeout(() => {
                this.focusRenameInput(newId);
            }, 10);
        },
        /**
         * 递归更新某节点所有子孙节点的 id 与 paramJsonPath
         * @param {*} node 当前 el-tree node
         * @param {string} parentPath 最新父级 jsonPath（不含当前节点 label）
         */
        updateChildrenPath(node, parentPath, rootPrefix) {
            if (!node || !node.data) return;
            // 判断父节点是否为数组类型，用于决定是否在路径中追加 [*]
            const parentIsArray = node.parent && this.isArrayNode(node.parent);
            let curPath;
            if (parentIsArray) {
                if (parentPath) {
                    curPath = `${parentPath}[*].${node.data.label}`;
                } else {
                    curPath = `${node.parent.data.label}[*].${node.data.label}`;
                }
            } else {
                if (parentPath) {
                    curPath = `${parentPath}.${node.data.label}`;
                } else {
                    curPath = node.data.label;
                }
            }

            // 更新表单数据
            const newJsonPath = `$.${curPath}`;
            if (node.data.formData) {
                this.$set(node.data.formData, 'paramJsonPath', newJsonPath);
            } else if (node.data.relatedParams && node.data.relatedParams.length) {
                this.$set(node.data.relatedParams[0], 'paramJsonPath', newJsonPath);
            }

            // 递归处理子节点
            if (Array.isArray(node.childNodes)) {
                node.childNodes.forEach((child) => {
                    this.updateChildrenPath(child, curPath, rootPrefix);
                });
            }
        },
        /**
         * 删除节点及其子孙节点对应的表单数据
         * @param {*} node el-tree 节点
         */
        removeFormDataOfNode(node) {
            if (!node) return;
            const collect = [];
            const dfs = (n) => {
                if (n.data.formData) collect.push(n.data.formData);
                if (n.childNodes && n.childNodes.length) {
                    n.childNodes.forEach(dfs);
                }
            };
            dfs(node);
            if (collect.length) {
                this.model = this.model.filter((p) => !collect.includes(p));
            }
        },
        /** 删除当前节点（根节点不允许删除） */
        deleteNode(node) {
            if (node.level === 1) return;
            // 先移除表单数据
            this.removeFormDataOfNode(node);

            const parent = node.parent;
            if (!parent) return;
            const index = parent.data.children.findIndex((n) => n.id === node.data.id);
            if (index > -1) {
                parent.data.children.splice(index, 1);
            }
            this.paramsInfoForm = {};
        },
        /** 重命名节点（根节点不允许） */
        renameNode(node) {
            if (node.level === 1) return;
            this.editNodeId = node.data.id;
            this.renamePrevLabel = node.data.label;
            // 关闭 popover 并聚焦输入框
            this.$nextTick(() => {
                const pop = this.$refs[`popover-${node.data.id}`];
                if (pop && pop.doClose) pop.doClose();
                // 再次 nextTick 等待输入框渲染完成
                this.$nextTick(() => {
                    const inputRef = this.$refs[`renameInput-${node.data.id}`];
                    if (inputRef && inputRef.focus) {
                        inputRef.focus();
                    } else if (inputRef && inputRef.$el) {
                        const inp = inputRef.$el.querySelector('input');
                        inp && inp.focus();
                    }
                });
            });
        },
        /** 完成重命名 */
        finishRename(node) {
            const trimmed = (node.data.label || '').trim();
            const emptyLabel = !trimmed;

            // 处理空标签情况（包括新增未输入或重命名清空）
            this.handleEmptyLabel(node, emptyLabel);

            // 如果最终仍为空标签，则不再继续后续流程，防止产生空 paramNameEn
            if (!trimmed) {
                this.closeAllPopovers();
                this.editNodeId = null;
                this.renamePrevLabel = '';
                return;
            }

            // 非空时做重名校验
            // 先做参数名格式校验：以字母开头，只允许字母、数字、下划线或连字符
            const namePattern = /^[A-Za-z][A-Za-z0-9_-]*$/;
            if (!namePattern.test(trimmed)) {
                this.$message.warning('参数名需以字母开头，且仅包含字母、数字、下划线或连字符');
                // 若是重命名则恢复旧名
                if (this.renamePrevLabel) {
                    node.data.label = this.renamePrevLabel;
                }
                // 重新聚焦输入框
                this.$nextTick(() => {
                    this.focusRenameInput(node.data.id);
                });
                return;
            }

            // 通过格式校验后继续做重名校验
            if (this.hasDuplicateSibling(node)) {
                // 若重名，保留编辑状态并提示
                this.$message.warning('同级下已存在同名！');
                // 若是重命名（renamePrevLabel 有值）则恢复原名称
                if (this.renamePrevLabel) {
                    node.data.label = this.renamePrevLabel;
                }
                // 重新聚焦输入框
                this.$nextTick(() => {
                    this.focusRenameInput(node.data.id);
                });
                return; // 终止后续流程
            }

            // 通过校验后更新节点和表单
            this.updateNodeAndFormData(node);

            // 切换到当前节点并关闭编辑
            this.handleParamsInfoTreeClick(node.data, node);

            this.closeAllPopovers();
            this.editNodeId = null;
            this.renamePrevLabel = '';
        },

        /**
         * 处理空标签情况
         */
        handleEmptyLabel(node, emptyLabel) {
            if (emptyLabel && !this.renamePrevLabel) {
                // 新增节点且未输入，撤销新增
                const parent = node.parent;
                if (parent && Array.isArray(parent.data.children)) {
                    const idx = parent.data.children.findIndex((n) => n.id === node.data.id);
                    if (idx !== -1) parent.data.children.splice(idx, 1);
                }
            } else if (emptyLabel) {
                // 重命名时输入为空，恢复之前标签
                node.data.label = this.renamePrevLabel;
            }
        },

        /**
         * 更新节点和表单数据
         */
        updateNodeAndFormData(node) {
            // 计算路径信息
            const pathInfo = this.calculateNodePath(node);
            const { segments, newPath, rootPrefix } = pathInfo;

            // 更新节点 ID
            node.data.id = `${rootPrefix}.${segments.join('.')}`;
            node.data.jsonPath = newPath.replace(/^\$\./, '');
            this.currentNodeKey = node.data.id;

            // 更新表单数据
            this.updateFormData(node, newPath);

            // 更新子节点路径
            if (node.childNodes && node.childNodes.length) {
                node.childNodes.forEach((child) =>
                    this.updateChildrenPath(child, segments.join('.'), rootPrefix)
                );
            }
        },

        /**
         * 计算节点路径信息
         */
        calculateNodePath(node) {
            const segments = [];
            let cur = node;
            const originNode = node;

            // level 从 1 开始：根节点 level=1，不计入 jsonPath
            while (cur && cur.level > 1) {
                let segmentLabel = cur.data.label;
                // 祖先节点为数组类型时，需在路径中追加 [*]
                if (cur !== originNode && this.isArrayNode(cur)) {
                    segmentLabel += '[*]';
                }
                segments.unshift(segmentLabel);
                cur = cur.parent;
            }

            const newPath = `$.${segments.join('.')}`;

            // 获取根节点前缀，确保不同根下 id 不冲突
            const rootNode = this.getRootNode(node);
            let rootPrefix = 'root';
            if (rootNode) {
                rootPrefix = rootNode.data.id;
            }

            return { segments, newPath, rootPrefix };
        },

        /**
         * 更新表单数据
         */
        updateFormData(node, newPath) {
            if (node.data.formData) {
                this.$set(node.data.formData, 'paramJsonPath', newPath);
                this.$set(node.data.formData, 'paramNameEn', node.data.label);
            } else if (node.data.relatedParams && node.data.relatedParams.length) {
                this.$set(node.data.relatedParams[0], 'paramJsonPath', newPath);
                this.$set(node.data.relatedParams[0], 'paramNameEn', node.data.label);
            } else {
                this.createNewFormData(node, newPath);
            }
        },

        /**
         * 创建新的表单数据
         */
        createNewFormData(node, newPath) {
            // 判断属于 IN 还是 OUT，根据 level ===1 的根节点
            const rNode = this.getRootNode(node);
            let paramInout = 'IN';
            if (rNode && rNode.data.id === 'out-params') {
                paramInout = 'OUT';
            }
            let isLeafNode = 1;
            if (node.children && node.children.length > 0) {
                isLeafNode = 0;
            }

            const newFormData = {
                paramNameEn: node.data.label,
                paramInout,
                paramName: '',
                paramType: '',
                paramDefaultValue: '',
                paramInType: 'null',
                paramDescription: '',
                paramJsonPath: newPath,
                isRequired: 0,
                isValid: 1,
                paramComment: '',
                paramHandleChoice: 0,
                isLeafNode
            };

            this.$set(node.data, 'formData', newFormData);
            // 将新的参数加入整体模型数组
            this.model = [...this.model, newFormData];
        },
        closeAllPopovers() {
            Object.entries(this.$refs).forEach(([k, ref]) => {
                const closeFn = (r) => {
                    if (r && r.doClose) r.doClose();
                };
                if (Array.isArray(ref)) {
                    ref.forEach(closeFn);
                } else {
                    closeFn(ref);
                }
            });
        },
        handlePopoverShow(node) {
            Object.entries(this.$refs).forEach(([k, ref]) => {
                if (!k.startsWith('popover-')) return;
                if (k === `popover-${node.data.id}`) return;
                const closeFn = (r) => {
                    if (r && r.doClose) r.doClose();
                };
                if (Array.isArray(ref)) {
                    ref.forEach(closeFn);
                } else {
                    closeFn(ref);
                }
            });
            this.popoverNodeId = node.data.id;
        },
        handlePopoverHide() {
            this.popoverNodeId = null;
        },
        /** 获取当前节点所属的根节点（level===1） */
        getRootNode(node) {
            let root = node;
            while (root && root.level > 1) {
                root = root.parent;
            }
            return root;
        },
        /** 判断节点是否为数组类型 */
        isArrayNode(node) {
            if (!node || !node.data) return false;
            if (node.data.formData && node.data.formData.paramType === 'array') return true;
            if (
                node.data.relatedParams &&
                node.data.relatedParams.length &&
                node.data.relatedParams[0].paramType === 'array'
            ) {
                return true;
            }
            return false;
        },
        /**
         * 判断在同一父节点下是否存在重名 label
         */
        hasDuplicateSibling(node) {
            const parent = node.parent;
            if (!parent || !parent.data || !Array.isArray(parent.data.children)) return false;
            const curLabel = (node.data.label || '').trim();
            return parent.data.children.some(
                (c) => c !== node.data && (c.label || '').trim() === curLabel
            );
        },
        /**
         * 判断是否应该显示新增子节点按钮
         * 根据 paramsInfoRadioValue 和节点类型控制显示
         */
        shouldShowAddButton(node) {
            // raw 模式（值为3）：保持现有行为，所有节点都可以新增子节点
            if (this.paramsInfoRadioValue === 3) {
                return true;
            }

            // form-data 模式（值为2）：限制入参信息节点的子节点新增功能
            if (this.paramsInfoRadioValue === 2) {
                // 如果是根节点（level === 1），允许新增
                if (node.level === 1) {
                    return true;
                }

                // 获取当前节点所属的根节点
                const rootNode = this.getRootNode(node);

                // 如果属于入参信息节点（in-params），禁止新增子节点
                if (rootNode && rootNode.data.id === 'in-params') {
                    return false;
                }

                // 其他情况（如出参信息节点）允许新增
                return true;
            }

            // 默认情况允许新增
            return true;
        }
    }
};
</script>

<style scoped lang="less">
.params-info-radio-group {
    height: 2rem;
    display: table;
    table-layout: fixed;
    width: fit-content;
    .el-radio-button {
        display: table-cell;
        width: 100%;
        height: 100%;
        margin: 0;
        &.is-active {
            .el-radio-button__inner {
                color: #1565ff;
                border-color: #1565ff;
                background-color: #1565ff1a;
                border-radius: 4px;
            }
        }
        &__inner {
            font-family:
                PingFangSC,
                PingFang SC;
            font-weight: 400;
            font-size: 14px;
            color: rgba(0, 0, 0, 0.65);
            line-height: 20px;
            padding: 0 20px;
            height: 100%;
            display: flex;
            align-items: center;
            justify-content: center;
        }
    }
}

.params-info-content {
    margin-top: 1rem;
    height: 25.25rem;
    background: #ffffff;
    border-radius: 0.25rem;
    border: 0.0625rem solid #d6dae0;
    display: flex;
    overflow: hidden;
    .content-left {
        width: 17.5rem;
        height: 100%;
        border-radius: 0.25rem 0 0 0.25rem;
        border-right: 0.0625rem solid #d6dae0;
        padding: 1rem 0.625rem;
        display: flex;
        flex-direction: column;
        gap: 0.625rem;
        .left-header {
            display: flex;
            align-items: center;
            gap: 0.25rem;
            padding: 0;
            span {
                font-family:
                    PingFangSC,
                    PingFang SC;
                font-weight: 500;
                font-size: 0.875rem;
                color: rgba(0, 0, 0, 0.85);
            }
        }
        .left-main {
            --bar-width: 3px;
            --thumb-color: #dfdfdf;
            min-height: 0;
            flex: 1;
            overflow-y: auto;
            .left-el-tree {
                height: 100%;
                width: 100%;
            }
        }
    }
    .content-right {
        min-width: 0;
        flex: 1;
        height: 100%;
        padding: 1.5rem;
        .no-params-tip {
            display: flex;
            align-items: center;
            justify-content: center;
            height: 100%;
            span {
                font-family:
                    PingFangSC,
                    PingFang SC;
                font-weight: 400;
                font-size: 14px;
                color: rgba(0, 0, 0, 0.45);
            }
        }
    }
}

.root-node-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    &::before {
        content: '';
        width: 16px;
        height: 16px;
        margin-right: 8px;
        background: url(../../../../../img/common/note-open.png) no-repeat center;
        background-size: 100% 100%;
    }
}

.node-wrapper {
    display: inline-flex;
    align-items: center;
    min-width: 0;
    flex: 1;
    height: 100%;
    position: relative;
    .rename-input {
        width: 100%;
        height: 100%;
        display: flex;
        align-items: center;
        justify-content: center;
        /deep/ .el-input__inner {
            height: 90%;
            line-height: 20px;
            padding: 0 4px;
            font-size: 14px;
            display: flex;
            align-items: center;
            justify-content: center;
        }
    }

    .node-actions {
        position: absolute;
        right: 6px;
        top: 50%;
        transform: translateY(-50%);
        height: fit-content;
        display: inline-flex;
        align-items: center;
        gap: 4px;
        border-radius: 6px;
        .action-icon {
            cursor: pointer;
            font-size: 12px;
            font-weight: 700;
            color: rgba(0, 0, 0, 0.65);
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .operation-action {
            width: 20px;
            height: 20px;
            border-radius: 4px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            &:hover {
                background: rgba(255, 255, 255, 0.8);
                .action-icon {
                    color: #1565ff;
                }
            }
        }
    }
}

/deep/ .params-info-radio-group {
    height: 2rem;
    display: table;
    table-layout: fixed;
    width: fit-content;
    .el-radio-button {
        display: table-cell;
        width: 100%;
        height: 100%;
        margin: 0;
        &.is-active {
            .el-radio-button__inner {
                color: #1565ff;
                border-color: #1565ff;
                background-color: #1565ff1a;
                border-radius: 4px;
            }
        }
        &__inner {
            font-family:
                PingFangSC,
                PingFang SC;
            font-weight: 400;
            font-size: 14px;
            color: rgba(0, 0, 0, 0.65);
            line-height: 20px;
            padding: 0 20px;
            height: 100%;
            display: flex;
            align-items: center;
            justify-content: center;
        }
    }
}

/deep/ .el-input__inner:focus {
    border: 0.0625rem solid #1565ff;
    box-shadow: 0 0 0 0.125rem rgba(21, 101, 255, 0.2);
}
</style>
<style lang="less">
.popover-action.more-btn-wrapper {
    display: flex;
    flex-direction: column;
    gap: 4px;
    padding: 0;
    .popover-action-item {
        display: flex;
        align-items: center;
        justify-content: flex-start;
        gap: 8px;
        cursor: pointer;
        padding: 4px 8px;
        i {
            font-size: 14px;
        }
        span {
            font-family:
                PingFangSC,
                PingFang SC;
            font-weight: 400;
            font-size: 14px;
            color: rgba(0, 0, 0, 0.65);
        }
        .el-icon-delete {
            color: #ff4d4f;
        }
        .el-icon-edit {
            color: rgba(0, 0, 0, 0.65);
        }
        &:hover {
            background: rgba(0, 0, 0, 0.04);
            border-radius: 4px;
        }
    }
}
</style>
