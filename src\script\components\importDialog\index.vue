<template>
    <el-dialog
        :title="title || (type === 'excel' ? '从 Excel 导入' : '从 JSON 导入')"
        :visible.sync="dialogVisible"
        width="760px"
        :close-on-click-modal="false"
        :before-close="handleClose"
        destroy-on-close
    >
        <by-excel v-if="type === 'excel'" @on-success="onSuccess" @on-cancel="onCancel" />
        <by-json v-else-if="type === 'json'" @on-success="onSuccess" @on-cancel="onCancel" />
        <template v-else>
            <div class="error-msg">未知导入类型</div>
        </template>
    </el-dialog>
</template>

<script>
import ByExcel from './ByExcel.vue';
import ByJson from './ByJson.vue';

export default {
    name: 'ImportDialog',
    components: {
        ByExcel,
        ByJson
    },
    props: {
        // 导入类型：excel 或 json
        type: {
            type: String,
            required: true,
            validator: (value) => ['excel', 'json'].includes(value)
        },
        // 弹窗标题
        title: {
            type: String,
            default: ''
        },
        // 是否显示弹窗
        visible: {
            type: Boolean,
            default: false
        }
    },
    data() {
        return {
            dialogVisible: this.visible
        };
    },
    watch: {
        visible(val) {
            this.dialogVisible = val;
        },
        dialogVisible(val) {
            this.$emit('update:visible', val);
        }
    },
    methods: {
        // 关闭弹窗
        handleClose() {
            this.dialogVisible = false;
            this.$emit('update:visible', false);
            this.$emit('on-cancel');
        },
        // 导入成功
        onSuccess(data) {
            this.$emit('on-success', data);
            this.dialogVisible = false;
        },
        // 取消导入
        onCancel() {
            this.$emit('on-cancel');
            this.dialogVisible = false;
        }
    }
};
</script>

<style lang="less" scoped>
.error-msg {
    color: #f56c6c;
    text-align: center;
    padding: 20px;
}
</style>
