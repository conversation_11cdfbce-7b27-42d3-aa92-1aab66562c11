import GatewayAxios from './gatewayAxios.js';
import { baseMixin } from 'mtex-rams-core';
//以下参数应从系统级获取，但因未作对接，先暂用固定值开发。
const operateUserId = '220551014393053187'; //操作人id
const sourceSystemId = 'FE2BF9D29678468CB56F78BFA8B99294'; //服务消费方id
const sourceSystemName = '德院'; //服务消费方名称
//真实调用接口使用的租户信息。
const POST_SUCCESS = '000001';
export let rel_operateUserId = '';
export let rel_sourceSystemId = '';
export let rel_sourceSystemName = '';
export default {
    userInfor: {
        provinceCode: '',
        provinceName: ''
    },
    install($vue, store) {
        //全局混入
        $vue.mixin(baseMixin);

        $vue.prototype.$CreateAxiox = ($this) => {
            return new GatewayAxios({
                rel_operateUserId,
                rel_sourceSystemId,
                rel_sourceSystemName
            });
        };
        $vue.prototype.$Update = ($this) => {
            let umName = $this.$store.getters.user.name;
            let gateway = new GatewayAxios({ operateUserId, sourceSystemId, sourceSystemName });
            gateway
                .update(umName)
                .then((res) => {
                    let data = res.data;
                    rel_operateUserId = data.operateUserId;
                    rel_sourceSystemId = data.sourceSystemId;
                    rel_sourceSystemName = data.sourceSystemName;
                    this.userInfor = {
                        provinceCode: data.provinceCode,
                        provinceName: data.provinceName
                    };
                })
                .catch((err) => {
                    console.log('错误信息：', err);
                });
        };
    },

    getTenant(umName) {
        return new Promise((resolve, reject) => {
            let gateway = new GatewayAxios({ operateUserId, sourceSystemId, sourceSystemName });
            gateway
                .update(umName)
                .then((res) => {
                    if (res.returnCode == POST_SUCCESS) {
                        let data = res.data || [];
                        rel_operateUserId = data.operateUserId;
                        rel_sourceSystemId = data.sourceSystemId;
                        rel_sourceSystemName = data.sourceSystemName;
                        this.userInfor = {
                            provinceCode: data.provinceCode,
                            provinceName: data.provinceName
                        };
                    }
                    resolve(res);
                    return;
                })
                .catch((err) => {
                    console.log('错误信息：', err);
                    reject({
                        success: err,
                        errorMessage: err
                    });
                });
        });
    },

    async initTenant(umName) {
        const tenantData = await this.getTenant(umName);
        return tenantData;
    }
};
