<template>
    <el-card class="metric-card" shadow="hover" :body-style="{ padding: '1rem' }">
        <div class="metric-card-body">
            <img :src="icon" class="metric-icon" />
            <div class="metric-info">
                <div class="metric-title">{{ title }}</div>
                <div class="metric-value">{{ value }}</div>
            </div>
        </div>
    </el-card>
</template>
<script>
export default {
    name: 'MetricCard',
    props: {
        title: { type: String, required: true },
        value: { type: [String, Number], required: true },
        icon: { type: String, default: '' }
    }
};
</script>
<style scoped lang="less">
.metric-card {
    width: 100%;
    height: 5rem;
    background: #f6f8fa;
    border-radius: 0.25rem;
    border: 1px solid #ebedf0;
}
.metric-card-body {
    display: flex;
    align-items: center;
}
.metric-icon {
    width: 2.25rem;
    height: 2.25rem;
}
.metric-info {
    margin-left: 1.125rem;
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
}
.metric-title {
    font-family:
        PingFangSC,
        PingFang SC;
    font-weight: 400;
    font-size: 0.875rem;
    color: rgba(0, 0, 0, 0.65);
    line-height: 1.25rem;
}
.metric-value {
    font-family: DIN, DIN;
    font-weight: bold;
    font-size: 1.25rem;
    color: rgba(0, 0, 0, 0.85);
    line-height: 1.25rem;
}
</style>
