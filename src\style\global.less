.mcpservice-theme {
    @import './element-ui/index.less';

    // w-1到w-100
    .generate-all-widths(@n, @i: 1) when (@i <= @n) {
        .w-@{i} {
            width: percentage(@i / 100);
        }
        .generate-all-widths(@n, (@i + 1));
    }
    .generate-all-widths(100);

    // 自定义滚动条样式
    .custom-scrollbar {
        --bar-width: 0.375rem;
        --bar-height: 0.375rem;
        --thumb-color: #8b8b8b;
        &::-webkit-scrollbar {
            width: var(--bar-width);
            height: var(--bar-height);
        }
        &::-webkit-scrollbar-thumb {
            border-radius: 0.5rem;
            background: var(--thumb-color);
        }
        &::-webkit-scrollbar-track {
            /* 滚动条里面轨道 */
            border-radius: 0.5rem;
            background: transparent;
        }
        &::-webkit-scrollbar-corner {
            background: rgba(0, 0, 0, 0);
        }
    }

    // 自定义文本溢出
    .text-ellipsis {
        --line-clamp: 1;
        overflow: hidden;
        display: -webkit-box;
        line-clamp: var(--line-clamp);
        -webkit-line-clamp: var(--line-clamp);
        -webkit-box-orient: vertical;
        text-overflow: ellipsis;
    }

    // 自定义确认删除对话框
    &.confirm-title {
        font-family:
            PingFangSC,
            PingFang SC;
        font-weight: 500;
        font-size: 16px;
        color: rgba(0, 0, 0, 0.85);
        line-height: 24px;
    }

    &.confirm-desc {
        font-family:
            PingFangSC,
            PingFang SC;
        font-weight: 400;
        font-size: 14px;
        color: rgba(0, 0, 0, 0.65);
        line-height: 22px;
        margin: 0;
        padding-left: 32px;
    }
}
