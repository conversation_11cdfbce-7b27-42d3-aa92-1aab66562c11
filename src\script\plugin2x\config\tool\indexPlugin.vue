<template>
    <div class="config-tool">
        <div class="config-tool-container" v-show="!isEditing">
            <header class="config-tool-container-header">MCP工具配置</header>
            <div class="config-tool-container-search">
                <SearchBar :form-cols="searchFormCols" :form="searchForm">
                    <el-button type="primary" @click="handleSearch">查询</el-button>
                    <el-button
                        style="margin-left: auto"
                        type="plain"
                        icon="el-icon-download"
                        @click="openImportDialog('excel')"
                        >excel导入
                    </el-button>
                    <el-button
                        type="plain"
                        icon="el-icon-download"
                        @click="openImportDialog('json')"
                        >json导入
                    </el-button>
                    <el-button
                        type="primary"
                        icon="el-icon-plus"
                        @click="
                            editType = 'add';
                            isEditing = true;
                        "
                        >新增工具
                    </el-button>
                </SearchBar>
            </div>
            <main class="config-tool-container-main">
                <CardTable
                    v-loading="isLoading"
                    :data="toolTableData"
                    :total="total"
                    :layout="'total, prev, pager, next, sizes, jumper'"
                    :pagination="pagination"
                    :updateTable="getTableData"
                    :cardConfig="cardConfig"
                    @cardEvent="handleCardEvent"
                ></CardTable>
            </main>
        </div>
        <AddOrEditTool
            v-if="isEditing"
            :type="editType"
            :toolId="editToolId"
            @cancel="isEditing = false"
            @confirm="handleConfirm"
        />
        <ImportDialog
            :type="dialogType"
            :visible.sync="dialogVisible"
            @on-success="handleImportSuccess"
        />
    </div>
</template>

<script>
import SearchBar from '@/script/components/SearchBar.vue';
import CardTable from '@/script/components/cardTable/index.vue';
import ConfigTool from '@/script/api/module/config-tool';
import ImportDialog from '@/script/components/importDialog/index.vue';
import AddOrEditTool from './AddOrEditTool.vue';

export default {
    name: 'ToolConfig',
    components: {
        SearchBar,
        CardTable,
        ImportDialog,
        AddOrEditTool
    },
    data() {
        return {
            isEditing: false,
            editType: 'add',
            editToolId: '',
            isLoading: false,
            // 搜索表单
            serviceProjectOpts: [],
            serviceModuleOpts: [],
            searchForm: {
                toolDescription: '',
                toolName: '',
                isValid: '',
                serviceProject: [],
                serviceModule: []
            },
            // 导入弹窗
            dialogVisible: false,
            dialogType: 'excel', // 'excel' 或 'json'
            // 工具表格数据
            toolTableData: [],
            total: 0,
            pagination: {
                curPage: 1,
                pageSize: 10
            },
            // 卡片配置
            cardConfig: {
                module: 'tool',
                icon: {
                    0: require('../../../../img/common/tool-inactive.png'),
                    1: require('../../../../img/common/tool-active.png')
                },
                editType: 'page',
                headerHideItem: ['setting', 'tenantKey'],
                navList: [
                    {
                        name: '基础信息',
                        value: 'ToolBaseInfo'
                    },
                    {
                        name: '入参信息',
                        value: 'ToolInputInfo'
                    },
                    {
                        name: '出参信息',
                        value: 'ToolOutputInfo'
                    },
                    // {
                    //     name: '工具参数信息',
                    //     value: 'ToolFormDataInfo'
                    // },
                    {
                        name: '接口服务',
                        value: 'ToolApiInfo'
                    }
                ]
                // 配置卡片里面的nav项啥时候不显示
                // navListFilterFun: (card, navList) => {
                //     const type = card.rawData.toolSource;
                //     const RULES = {
                //         2: ['ToolInputInfo', 'ToolOutputInfo'],
                //         1: ['ToolFormDataInfo']
                //     };

                //     const originalNavList = [...navList];
                //     const newNavList = originalNavList.map((item) => {
                //         const newItem = { ...item };

                //         if (RULES.hasOwnProperty(type)) {
                //             if (RULES[type].includes(item.value)) {
                //                 newItem.isShow = false;
                //             }
                //         }

                //         return newItem;
                //     });

                //     return newNavList;
                // }
            }
        };
    },
    computed: {
        searchFormCols() {
            return [
                [
                    {
                        type: 'el-input',
                        prop: 'toolDescription',
                        label: '工具描述：',
                        labelWidth: 'max-content',
                        attrs: {
                            placeholder: '请输入',
                            clearable: true
                        },
                        rules: [],
                        span: 4,
                        isDisabled: false,
                        isShow: true,
                        opts: []
                    },
                    {
                        type: 'el-input',
                        prop: 'toolName',
                        label: '工具名称：',
                        labelWidth: 'max-content',
                        attrs: {
                            placeholder: '请输入',
                            clearable: true
                        },
                        rules: [],
                        span: 4,
                        isDisabled: false,
                        isShow: true,
                        opts: []
                    },
                    {
                        type: 'el-select',
                        prop: 'isValid',
                        label: '状态：',
                        labelWidth: 'max-content',
                        attrs: {
                            placeholder: '请选择',
                            clearable: false
                        },
                        rules: [],
                        span: 4,
                        isDisabled: false,
                        isShow: true,
                        opts: [
                            {
                                label: '全部',
                                value: ''
                            },
                            {
                                label: '起效',
                                value: 1
                            },
                            {
                                label: '不起效',
                                value: 0
                            }
                        ]
                    },
                    {
                        type: 'DropdownCheckBox',
                        prop: 'serviceProject',
                        label: '',
                        labelWidth: 'max-content',
                        attrs: {
                            label: '所属项目',
                            options: this.serviceProjectOpts
                        },
                        rules: [],
                        span: 2,
                        isDisabled: false,
                        isShow: true,
                        opts: []
                    },
                    {
                        type: 'DropdownCheckBox',
                        prop: 'serviceModule',
                        label: '',
                        labelWidth: 'max-content',
                        attrs: {
                            label: '所属模块',
                            options: this.serviceModuleOpts
                        },
                        rules: [],
                        span: 2,
                        isDisabled: false,
                        isShow: true,
                        opts: []
                    },
                    {
                        type: 'tool',
                        prop: 'tool',
                        span: 8,
                        isShow: true
                    }
                ]
            ];
        }
    },
    created() {
        this.getTableData(this.pagination);
    },
    methods: {
        handleSearch() {
            this.pagination.curPage = 1;
            this.getTableData(this.pagination);
        },
        getTableData({ curPage = 1, pageSize = this.pagination.pageSize }) {
            this.isLoading = true;
            ConfigTool.getToolClassify({})
                .then((res) => {
                    this.serviceProjectOpts = res.data.serviceProject;
                    this.serviceModuleOpts = res.data.serviceModule;
                })
                .then(() => {
                    ConfigTool.getToolList({
                        pageNum: curPage,
                        pageSize: pageSize,
                        toolDescription: this.searchForm.toolDescription,
                        toolName: this.searchForm.toolName,
                        isValid: this.searchForm.isValid,
                        serviceProjectList: this.searchForm.serviceProject,
                        serviceModuleList: this.searchForm.serviceModule
                    }).then((res) => {
                        if (res.serviceFlag === 'TRUE') {
                            this.toolTableData = res.data.list
                                .map((item) => {
                                    return {
                                        rawData: item,
                                        id: item.toolId,
                                        name: item.toolName,
                                        description: item.toolDescription,
                                        isValid: item.isValid,
                                        createdTime: item.createdTime,
                                        lastUpdateTime: item.lastUpdateTime
                                    };
                                })
                                .sort((a, b) => {
                                    return new Date(b.lastUpdateTime) - new Date(a.lastUpdateTime);
                                });
                            this.total = res.data.total;
                        }
                    });
                })
                .finally(() => {
                    this.isLoading = false;
                });
        },
        openImportDialog(val) {
            this.dialogType = val;
            this.dialogVisible = true;
        },
        // 导入成功回调
        handleImportSuccess(data) {
            console.log('导入成功:', data);
            // 处理导入结果数据
            this.$message.success('导入成功');
        },
        handleCardEvent(event) {
            const eventMap = {
                // 展开卡片
                expand: () => {
                    ConfigTool.getToolDetail({ toolId: event.params.id }).then((res) => {
                        if (res.serviceFlag === 'TRUE') {
                            event.callback(JSON.parse(JSON.stringify(res.data)));
                        } else {
                            this.$message.error(res.returnMsg || '获取工具详情失败');
                        }
                    });
                },
                // 复制卡片
                copy: () => {
                    this.editToolId = event.params.id;
                    this.editType = 'copy';
                    this.isEditing = true;
                },

                // 编辑卡片
                edit: () => {
                    this.editToolId = event.params.id;
                    this.editType = 'edit';
                    this.isEditing = true;
                },

                // 删除卡片
                delete: () => {
                    ConfigTool.delTool({
                        toolIds: [event.params]
                    }).then((res) => {
                        this.handleToolResponse(res, '删除成功', '删除失败');
                    });
                },

                // 切换卡片状态（有效/无效）
                valid: () => {
                    ConfigTool.changeToolValid({
                        toolIds: [event.params.id],
                        isValid: +event.params.isValid
                    }).then((res) => {
                        this.handleToolResponse(res, '切换成功', '切换失败');
                    });
                }
            };

            eventMap[event.type] && eventMap[event.type]();
        },
        handleConfirm() {
            this.handleSearch();
            this.isEditing = false;
        },
        handleToolResponse(res, successMsg, errorMsg) {
            if (res.serviceFlag === 'TRUE') {
                this.$message.success(res.returnMsg || successMsg);
                this.handleSearch();
            } else {
                this.$message.error(res.returnMsg || errorMsg);
            }
        }
    }
};
</script>

<style scoped lang="less">
.config-tool {
    width: 100%;
    height: 100%;
}
.config-tool-container {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    gap: 1rem;
    &-header {
        height: 1.25rem;
        margin-left: 0.5rem;
        font-family:
            PingFangSC,
            PingFang SC;
        font-weight: 600;
        font-size: 1rem;
        color: rgba(0, 0, 0, 0.85);
        line-height: 1.25rem;
    }
    &-search {
        height: 3.5rem;
        background: rgba(255, 255, 255, 0.96);
        border-radius: 0.375rem;
        display: flex;
        align-items: center;
        gap: 1rem;
        padding: 0 1rem;
    }
    &-main {
        height: 0;
        flex: 1;
        display: flex;
        gap: 1rem;
    }
    .dialog-footer {
        text-align: right;
        margin-top: 32px;
    }
}
</style>
