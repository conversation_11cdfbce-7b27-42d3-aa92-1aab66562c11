<template>
    <div class="excel-import">
        <div class="steps">
            <div class="step flex-row">
                <div class="step-content">1、下载导入模板，根据模板提示完善内容</div>
                <el-button
                    type="plain"
                    icon="el-icon-download"
                    class="download-template"
                    @click="downloadTemplate"
                    >下载模板
                </el-button>
            </div>

            <div class="step flex-col">
                <div class="step-content">2、上传完善好的内容，支持上传文件格式为：.xls、.xlsx</div>
                <div class="upload-area">
                    <el-upload
                        class="upload-box"
                        :class="{ 'is-dragover': isDragover }"
                        action="#"
                        :auto-upload="false"
                        :show-file-list="false"
                        :on-change="handleFileChange"
                        :on-remove="handleRemove"
                        :file-list="fileList"
                        accept=".xls,.xlsx"
                        multiple
                        drag
                    >
                        <img src="@/img/common/file-icon.svg" alt="upload" class="upload-icon" />
                        <div class="upload-text">
                            将文件拖到此处，或<span class="click-text">点击上传</span>
                        </div>
                    </el-upload>

                    <div class="file-list custom-scrollbar">
                        <div v-for="(file, index) in fileList" :key="index" class="file-item">
                            <img
                                src="@/img/common/file-icon-gray.svg"
                                alt="file"
                                class="file-icon"
                            />
                            <div class="file-info">
                                <span class="info-name">{{ file.name }}</span>
                                <span class="info-size" v-if="file.status === 'success'">{{
                                    formatFileSize(file.size)
                                }}</span>
                                <span class="info-progress" v-else>
                                    上传中
                                    <span class="info-progress-percent"
                                        >{{ file.percentage }}%
                                    </span>
                                </span>
                            </div>
                            <i class="el-icon-delete" @click="handleRemove(file)"></i>
                            <el-progress
                                v-if="file.status === 'uploading'"
                                class="info-progress-bar"
                                :percentage="file.percentage"
                                :show-text="false"
                                :stroke-width="2"
                                color="#1565FF"
                            ></el-progress>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="dialog-footer">
            <el-button @click="handleCancel">取消</el-button>
            <el-button type="primary" @click="handleConfirm" :disabled="!canConfirm"
                >确定</el-button
            >
        </div>
    </div>
</template>

<script>
export default {
    name: 'ByExcel',
    data() {
        return {
            fileList: [],
            uploadUrl: '/api/upload/excel', // 替换为实际的上传API
            templateUrl: '/api/download/template', // 替换为实际的模板下载API
            uploadStatus: '', // 'uploading', 'success', 'error'
            isDragover: false // 是否拖拽悬停
        };
    },
    computed: {
        canConfirm() {
            return (
                this.fileList.length > 0 && this.fileList.every((file) => file.status === 'success')
            );
        }
    },
    methods: {
        formatFileSize(size) {
            if (size < 1024) {
                return size + 'B';
            } else if (size < 1024 * 1024) {
                return (size / 1024).toFixed(2) + 'KB';
            }
            return (size / 1024 / 1024).toFixed(2) + 'MB';
        },
        // 处理文件选择变化
        handleFileChange(file, fileList) {
            // 检查文件类型
            const fileType = file.name.split('.').pop().toLowerCase();
            if (!['xls', 'xlsx'].includes(fileType)) {
                this.$message.error('只支持上传 .xls, .xlsx 格式的文件');
                // 移除不符合格式的文件
                this.fileList = this.fileList.filter((item) =>
                    ['xls', 'xlsx'].includes(item.name.split('.').pop().toLowerCase())
                );
                return;
            }

            // 转换文件列表，保留所有符合格式的文件
            const validFiles = fileList.filter((item) =>
                ['xls', 'xlsx'].includes(item.name.split('.').pop().toLowerCase())
            );

            this.fileList = validFiles.map((item) => ({
                name: item.name,
                size: item.size,
                raw: item.raw,
                status: 'uploading',
                percentage: 0
            }));

            // 为每个文件模拟上传进度
            this.fileList.forEach((file, index) => {
                this.simulateUpload(index);
            });
        },

        // 处理文件移除
        handleRemove(file) {
            // 从fileList中移除指定文件
            const index = this.fileList.findIndex((item) => item.name === file.name);
            if (index !== -1) {
                this.fileList.splice(index, 1);
            }
        },

        // 模拟上传进度 (实际项目中应替换为真实上传逻辑)
        simulateUpload(index) {
            let progress = 0;
            const interval = setInterval(() => {
                progress += Math.floor(Math.random() * 10);
                if (progress >= 100) {
                    progress = 100;
                    clearInterval(interval);

                    // 上传完成
                    this.$set(this.fileList[index], 'status', 'success');
                    this.$set(this.fileList[index], 'percentage', 100);
                } else {
                    this.$set(this.fileList[index], 'percentage', progress);
                }
            }, 300);
        },

        // 下载模板
        downloadTemplate() {
            this.$message.warning('暂未开放');
        },

        // 确认导入
        handleConfirm() {
            if (!this.canConfirm) return;

            // 实际项目中可以在这里进行正式的文件上传或处理
            // 这里模拟一个导入成功的结果
            const result = {
                success: true,
                data: {
                    files: this.fileList.map((file) => ({
                        name: file.name,
                        size: file.size
                    })),
                    total: 100,
                    success: 95,
                    fail: 5
                    // 可以根据需要添加更多返回信息
                }
            };

            this.$emit('on-success', result);
        },

        // 取消导入
        handleCancel() {
            this.$emit('on-cancel');
        }
    }
};
</script>

<style lang="less" scoped>
.excel-import {
    padding: 0 20px;

    .steps {
        display: flex;
        flex-direction: column;
        gap: 16px;
        .step {
            display: flex;
            background: #f6f8fa;
            border-radius: 4px;
            border: 1px dashed #d6dae0;
            padding: 14px 16px;
            position: relative;
            &.flex-row {
                flex-direction: row;
            }
            &.flex-col {
                flex-direction: column;
                gap: 16px;
            }

            .step-content {
                flex: 1;
                font-family:
                    PingFangSC,
                    PingFang SC;
                font-weight: 400;
                font-size: 14px;
                color: rgba(0, 0, 0, 0.85);
            }
            .download-template {
                background: #ffffff;
                border-radius: 4px;
                border: 1px solid #1565ff99;
                color: #1565ff;
                position: absolute;
                top: 50%;
                right: 16px;
                transform: translateY(-50%);
                &:hover {
                    background: #1565ff1a;
                }
            }
            .upload-area {
                margin: 0;
                display: flex;
                flex-direction: column;
                gap: 16px;

                .upload-box {
                    height: 144px;
                    background: #ffffff;
                    border-radius: 4px;
                    padding: 30px 0;
                    text-align: center;
                    cursor: pointer;
                    transition:
                        border-color 0.3s,
                        background-color 0.3s;

                    &:hover,
                    &.is-dragover {
                        border-color: #1565ff;
                        background-color: rgba(21, 101, 255, 0.06);
                    }

                    .upload-icon {
                        width: 34px;
                        height: 40px;
                        margin-bottom: 14px;
                    }

                    .upload-text {
                        color: #606266;

                        .click-text {
                            color: #1565ff;
                        }
                    }

                    // 重置el-upload样式
                    /deep/.el-upload-dragger {
                        width: 100%;
                        height: 100%;
                        border: none;
                        background: transparent;
                    }

                    /deep/.el-upload {
                        width: 100%;
                        height: 100%;
                        &__input {
                            display: none;
                        }
                    }
                }

                .file-list {
                    height: 128px;
                    display: flex;
                    flex-direction: column;
                    gap: 16px;
                    overflow-y: auto;

                    .file-item {
                        min-height: 56px;
                        background: #ffffff;
                        border-radius: 4px;
                        display: flex;
                        align-items: center;
                        padding: 8px 0;
                        position: relative;
                        .info-progress-bar {
                            position: absolute;
                            bottom: 0;
                            left: 0;
                            width: 100%;
                            height: 2px;
                            // background: #1565ff;
                        }
                        // &::after {
                        //     content: '';
                        //     width: 100%;
                        //     height: 2px;
                        //     background: #d6dae0; //#1565FF
                        // }

                        .file-icon {
                            width: 24px;
                            height: 24px;
                            margin-right: 10px;
                            margin-left: 16px;
                        }

                        .file-info {
                            flex: 1;
                            display: flex;
                            flex-direction: column;
                            gap: 2px;
                            .info-name {
                                font-family:
                                    PingFangSC,
                                    PingFang SC;
                                font-weight: 400;
                                font-size: 14px;
                                color: rgba(0, 0, 0, 0.85);
                            }
                            .info-size,
                            .info-progress {
                                font-family:
                                    PingFangSC,
                                    PingFang SC;
                                font-weight: 400;
                                font-size: 12px;
                            }
                            .info-size {
                                color: rgba(0, 0, 0, 0.65);
                            }
                            .info-progress {
                                color: #1565ff;
                                .info-progress-percent {
                                    margin-left: 4px;
                                }
                            }
                        }

                        .el-icon-delete {
                            margin-left: auto;
                            color: #909399;
                            cursor: pointer;
                            margin-right: 16px;

                            &:hover {
                                color: #f56c6c;
                            }
                        }
                    }
                }
            }
        }
    }

    .dialog-footer {
        text-align: right;
        margin-top: 20px;
    }
}
</style>
