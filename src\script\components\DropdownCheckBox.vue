<template>
    <el-popover
        class="dropdown-checkbox"
        :class="{ 'is-active': isOpen }"
        placement="bottom"
        trigger="click"
        v-model="isOpen"
        width="auto"
        popper-class="checkbox-popover"
    >
        <div class="dropdown-item">
            <el-checkbox :indeterminate="isIndeterminate" v-model="checkAll"
                ><span class="checkbox-label">全选</span></el-checkbox
            >
            <el-checkbox-group
                class="checkbox-group"
                v-model="checkedItems"
                @change="handleCheckedItemsChange"
            >
                <el-checkbox v-for="(item, index) in options" :label="item.name" :key="index">
                    <span class="checkbox-label" :title="item.name">{{ item.name }}</span>
                    <span class="checkbox-value">{{ `(${item.count})` }}</span>
                </el-checkbox>
            </el-checkbox-group>
        </div>
        <div slot="reference" class="popover-trigger">
            {{ label }} <i class="el-icon-arrow-down" :class="{ rotate: isOpen }"></i>
        </div>
    </el-popover>
</template>

<script>
export default {
    name: 'DropdownCheckBox',
    props: {
        label: {
            type: String,
            default: ''
        },
        options: {
            type: Array,
            default: () => []
        },
        value: {
            type: Array,
            default: () => []
        }
    },
    data() {
        return {
            isIndeterminate: false,
            isOpen: false,
            checkedItems: [],
            isInternalUpdate: false // 标记是否为内部更新，避免循环
        };
    },
    computed: {
        checkAll: {
            get() {
                return this.checkedItems.length === this.options.length && this.options.length > 0;
            },
            set(val) {
                if (val) {
                    this.checkedItems = this.options.map((item) => item.name);
                } else {
                    this.checkedItems = [];
                }
                this.isIndeterminate = false;
            }
        }
    },
    watch: {
        // 监听外部 value 变化，同步到内部 checkedItems
        value: {
            immediate: true,
            deep: true,
            handler(newVal) {
                if (this.isInternalUpdate) return; // 如果是内部更新触发的，跳过

                // 避免循环触发：只有当外部值与内部值不同时才更新
                if (JSON.stringify(newVal) !== JSON.stringify(this.checkedItems)) {
                    this.isInternalUpdate = true;
                    if (newVal && newVal.length > 0) {
                        this.checkedItems = [...newVal];
                    } else {
                        this.checkedItems = [];
                    }
                    this.$nextTick(() => {
                        this.isInternalUpdate = false;
                    });
                }
            }
        },
        // 当 options 改变全部选中
        options: {
            immediate: true,
            deep: true,
            handler(newOpts, oldOpts) {
                if (JSON.stringify(oldOpts)===JSON.stringify(newOpts)) {
                    return;
                }
                this.checkedItems = newOpts.map((item) => item.name);
            }
        },
        checkedItems: {
            immediate: true,
            deep: true,
            handler(val) {
                if (this.isInternalUpdate) return; // 如果是内部更新触发的，跳过

                // 总是向外部同步，让外部决定是否需要更新
                this.$emit('input', val);
            }
        }
    },
    methods: {
        handleCheckedItemsChange(value) {
            const checkedCount = value.length;
            if (checkedCount === this.options.length) {
                this.isIndeterminate = false;
            } else if (checkedCount === 0) {
                this.isIndeterminate = false;
            } else {
                this.isIndeterminate = true;
            }
        }
    }
};
</script>

<style lang="less" scoped>
.dropdown-checkbox {
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: space-between;
    border: 1px solid #dcdfe6;
    border-radius: 4px;
    padding: 0 15px;
    white-space: nowrap;
}
.is-active {
    border-color: #409eff;
}
/deep/.el-popover__reference-wrapper {
    width: 100%;
}
.popover-trigger {
    width: 100%;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: space-between;
    font-family:
        PingFangSC,
        PingFang SC;
    font-weight: 400;
    font-size: 14px;
    color: rgba(0, 0, 0, 0.85);

    .el-icon-arrow-down {
        transition: transform 0.3s;
        margin-left: 5px;

        &.rotate {
            transform: rotate(180deg);
        }
    }
}

.dropdown-item {
    width: fit-content;
    /deep/ .el-checkbox {
        margin-right: 0;
        display: flex;
        align-items: center;
        gap: 4px;
    }
    /deep/ .el-checkbox__label {
        padding: 0;
        display: flex;
        align-items: center;
        gap: 4px;
        flex: 1;
        min-width: 40px;
    }
    .checkbox-label {
        font-family:
            PingFangSC,
            PingFang SC;
        font-weight: 400;
        font-size: 14px;
        color: rgba(0, 0, 0, 0.85);
        flex: 1;
        --line-clamp: 1;
        overflow: hidden;
        display: -webkit-box;
        line-clamp: var(--line-clamp);
        -webkit-line-clamp: var(--line-clamp);
        -webkit-box-orient: vertical;
        text-overflow: ellipsis;
    }
    .checkbox-value {
        font-family:
            PingFangSC,
            PingFang SC;
        font-weight: 400;
        font-size: 14px;
        color: rgba(0, 0, 0, 0.45);
    }
}
.checkbox-group {
    display: flex;
    flex-direction: column;
    width: max-content;
    max-width: 200px;
    height: 360px;
    overflow-y: auto;
    --bar-width: 4px;
    --bar-height: 4px;
    --thumb-color: #c9c9c9;
    &::-webkit-scrollbar {
        width: var(--bar-width);
        height: var(--bar-height);
    }
    &::-webkit-scrollbar-thumb {
        border-radius: 8px;
        background: var(--thumb-color);
    }
    &::-webkit-scrollbar-track {
        /* 滚动条里面轨道 */
        border-radius: 8px;
        background: transparent;
    }
    &::-webkit-scrollbar-corner {
        background: rgba(0, 0, 0, 0);
    }
}
</style>
