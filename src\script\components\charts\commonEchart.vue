<template>
    <div class="out-box">
        <el-empty
            v-if="JSON.stringify(targetData) === '{}'"
            description="暂无数据"
            :image="require('../../../img/common/noDataMon.png')"
        >
        </el-empty>
        <div v-else :ref="refName" :id="refName" class="common-echart"></div>
    </div>
</template>

<script>
import * as echarts from 'echarts';
// 提取创建系列项的函数
const createSeriesItem = (name, data, type, lineType, color, unit) => {
    return {
        name,
        data,
        type: type || 'line',
        symbolSize: 5,
        smooth: false,
        unit,
        barWidth: '30%',
        itemStyle: {
            line: {
                normal: {
                    color,
                    borderColor: color,
                    borderWidth: 1
                },
                emphasis: {
                    color
                }
            },
            bar: {
                normal: {
                    color
                }
            }
        }[type],
        lineStyle: {
            type: lineType || 'solid',
            normal: {
                color
            }
        },
        areaStyle: (() => {
            if (type === 'line') {
                return {
                    color: {
                        type: 'linear',
                        x: 0,
                        y: 0,
                        x2: 0,
                        y2: 1,
                        colorStops: [
                            {
                                offset: 0,
                                color: color + '80' // 40% opacity
                            },
                            {
                                offset: 1,
                                color: color + '00' // 0% opacity
                            }
                        ]
                    }
                };
            }
            return undefined;
        })()
    };
};

const formatValue = (value, unit) => {
    let newVal = value;
    if (unit.includes('百万')) {
        newVal = (value / 10 ** 6).toFixed(2);
    }
    return newVal;
};

const initEcharts = (myChart, options) => {
    const seriesType = [];
    const option = {
        textStyle: {
            fontFamily: 'PingFangSC, PingFang SC'
        },
        title: {
            show: options.titleShow,
            left: 0,
            top: 0,
            text: options.title,
            textStyle: {
                color: 'rgba(0,0,0,0.65)',
                fontSize: 12,
                fontWeight: 500
            }
        },
        grid: {
            top: options.titleShow ? 70 : 40,
            left: 50,
            right: 15,
            bottom: 65
        },
        legend: {
            show: options.lengendShow,
            width: '80%',
            top: options.titleShow ? 33 : 3,
            right: 10,
            textStyle: {
                color: 'rgba(0,0,0,0.65)',
                fontSize: 12
            },
            itemWidth: 20,
            itemHeight: 12,
            itemGap: 10,
            data: [],
            formatter: function (params) {
                return params;
            }
        },
        tooltip: {
            trigger: 'axis',
            axisPointer: {
                type: 'line',
                animation: false
            }
        },
        dataZoom: [
            {
                type: 'inside',
                start: 0,
                end: 100
            },
            {
                start: 0,
                end: 100,
                bottom: 0,
                left: 50,
                right: 15,
                height: 20,
                showDetail: false,
                showDataShadow: false,
                backgroundColor: '#F6F8FA',
                fillerColor: '#DBE8FF',
                borderColor: '#00000000',
                borderRadius: 2,
                moveHandleSize: 0,
                handleIcon: 'image://' + require('../../../img/common/handle-icon.png'),
                handleSize: '30'
            }
        ],
        xAxis: {
            type: 'category',
            data: options.xAxisData,
            boundaryGap: true,
            axisTick: { show: true, inside: true, alignWithLabel: options.alignWithLabel },
            axisLabel: {
                color: 'rgba(0,0,0,0.65)',
                fontSize: 12,
                fontWeight: 400,
                margin: 10
            },
            axisLine: {
                show: true,
                lineStyle: {
                    color: '#D6DAE0'
                }
            },
            axisPointer: {
                show: true,
                type: 'line',
                lineStyle: {
                    color: '#D6DAE0'
                }
            }
        },
        yAxis: {
            type: 'value',
            name: options.yAxisName,
            nameTextStyle: {
                color: 'rgba(0,0,0,0.65)',
                fontSize: 12,
                fontWeight: 400,
                align: 'center',
                padding: [0, 0, 5, -45]
            },
            axisTick: { show: false },
            axisLabel: {
                margin: 4,
                fontSize: 12,
                color: 'rgba(0,0,0,0.65)',
                fontWeight: 400,
                formatter: function (val) {
                    return formatValue(val, options.yAxisName || '');
                }
            },
            axisLine: {
                show: false, // 是否显示坐标轴轴线
                lineStyle: {
                    color: '#D6DAE0'
                }
            },
            splitLine: {
                show: true,
                lineStyle: {
                    type: 'solid',
                    color: '#D6DAE0'
                }
            }
        },
        series: []
    };

    // 原有的处理逻辑
    options.lineData.forEach((item, index) => {
        seriesType.push(item.lineType || 'default');

        // 使用提取的函数创建系列项
        const seriesItem = createSeriesItem(
            item.name,
            item.data,
            item.type,
            item.lineType,
            item.color,
            item.unit
        );

        option.series.push(seriesItem);

        option.legend.data.push({
            name: item.name,
            itemStyle: {
                color: item.color || 'rgba(118, 131, 143, 1)',
                borderWidth: 0
            }
        });

        if (item.isRate) {
            option.yAxis.min = 0;
            option.yAxis.max = 100;
            option.yAxis.axisLabel.formatter = function (val) {
                return val + '%';
            };
        }
    });

    myChart.clear();
    myChart.setOption(option);
};
export default {
    name: 'commonEchart',
    /**
     * targetData: {
                title: '每分钟记录总量',
                titleShow: true,
                xAxisData: [
                    '2024-01-09\n13:00',
                    '2024-01-09\n13:05',
                    '2024-01-09\n13:10',
                    '2024-01-09\n13:15',
                    '2024-01-09\n13:25',
                    '2024-01-09\n13:30',
                    '2024-01-09\n13:35',
                    '2024-01-09\n13:40',
                    '2024-01-09\n13:45',
                    '2024-01-09\n13:50',
                    '2024-01-09\n13:55',
                    '2024-01-09\n14:00'
                ],
                yAxisName: '记录数（条）',
                lengendShow: true,
                lineData: [
                    {
                        name: '今日',
                        type: 'line',
                        data: [
                            6423, 2000, 7248, 5432, 17084, 4824, 7442, 20270, 3215, 8527, 7473, 7413
                        ],
                        isRate: false,
                        unit: '条',
                        lineType: 'solid', // type为bar时，不要设置lineType
                        color: '#01E6FE'
                    },
                    {
                        name: '昨日',
                        type: 'line',
                        data: [
                            3215, 8527, 7473, 7413, 6423, 2000, 7248, 5432, 7084, 4824, 7442, 22700
                        ],
                        unit: '条',
                        lineType: 'dashed',
                        color: '#FFDF12'
                    }
                ],
            }
     */
    props: {
        targetData: {
            type: Object,
            default: () => ({})
        },
        refName: {
            type: String,
            default: 'common-echart'
        }
    },
    data() {
        return {
            myChart: '',
            resizeObserver: null
        };
    },
    watch: {
        targetData: {
            handler(newV) {
                if (newV.lineData) {
                    // 只更新图表配置，不重新初始化
                    this.updateEchart();
                }
            },
            deep: true
        }
    },
    mounted() {
        if (this.targetData && this.targetData.lineData) {
            this.initEchart();
        }
    },
    beforeDestroy() {
        if (this.resizeObserver) {
            this.resizeObserver.disconnect();
            this.resizeObserver = null;
        }
        if (this.myChart) {
            this.myChart.dispose();
            this.myChart = null;
        }
    },
    methods: {
        initEchart() {
            this.$nextTick(() => {
                // 只有当图表实例不存在时才初始化
                if (!this.myChart) {
                    this.myChart = echarts.init(document.getElementById(this.refName));
                }
                initEcharts(this.myChart, this.targetData);
                this.resizeObserver = new ResizeObserver(() => {
                    this.myChart.resize();
                });
                this.resizeObserver.observe(this.$refs[this.refName]);
            });
        },
        updateEchart() {
            this.$nextTick(() => {
                // 确保图表实例已经创建
                if (!this.myChart) {
                    this.initEchart();
                    return;
                }
                // 只更新配置，不重新创建实例
                initEcharts(this.myChart, this.targetData);
            });
        }
    }
};
</script>

<style lang="less" scoped>
.out-box {
    width: 100%;
    height: 100%;
    position: relative;
}
.common-echart {
    width: 100%;
    height: 100%;
}
.el-empty {
    height: 100%;
}
</style>
