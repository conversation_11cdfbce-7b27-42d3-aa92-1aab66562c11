<template>
    <div class="resource-info-content">
        <header class="content-header">
            <span>可用资源</span>
            <el-button
                v-if="!readonly"
                type="plain"
                class="header-btn"
                icon="el-icon-link"
                @click="openResourceDialog"
            >
                关联资源
            </el-button>
        </header>
        <main class="content-main custom-scrollbar" v-if="value.resourceList.length > 0">
            <div
                class="main-item"
                :class="{ active: expandList.includes(item.resourceId) }"
                v-for="(item, index) in value.resourceList"
                :key="index"
                @click.stop="handleExpandClick(item.resourceId)"
            >
                <header @click.stop="handleExpandClick(item.resourceId)">
                    <div class="header-left">
                        <div class="title">
                            <img class="title-icon" :src="titleIcon[item.isValid]" alt="" />
                            <span class="title-text">{{ item.resourceName }}</span>
                        </div>
                        <div
                            class="desc"
                            :class="{ 'text-ellipsis': !expandList.includes(item.resourceId) }"
                            :title="item.resourceDescription"
                        >
                            {{ item.resourceDescription }}
                        </div>
                    </div>
                    <div class="header-right">
                        <div
                            v-if="!readonly"
                            class="delete-btn"
                            @click.stop="handleDelete(item.resourceId)"
                        >
                            <i class="el-icon-delete"></i>
                        </div>
                        <div class="dropdown-btn">
                            <i
                                class="el-icon-arrow-down header-expand-icon"
                                :class="{ rotate180: expandList.includes(item.resourceId) }"
                                @click.stop="handleExpandClick(item.resourceId)"
                            ></i>
                        </div>
                    </div>
                </header>
                <main v-if="expandList.includes(item.resourceId)" @click.stop>
                    <div class="main-content">
                        <div
                            class="main-content-item"
                            v-for="(value, key) in resourceDetail"
                            :key="key"
                        >
                            <p>
                                <span class="main-content-item-key">{{ key }}：</span>
                                <span class="main-content-item-value text-ellipsis" :title="value"
                                    >{{ value }}
                                </span>
                            </p>
                        </div>
                    </div>
                </main>
            </div>
        </main>
        <el-empty
            v-else
            description="暂无数据"
            :image="require('@/img/common/noDataMon.png')"
            style="width: 100%; height: 100%"
        >
        </el-empty>
        <!-- 关联资源弹窗 -->
        <el-dialog
            title="MCP服务关联资源配置"
            :visible.sync="resourceDialogVisible"
            width="1280px"
            :close-on-click-modal="false"
            destroy-on-close
        >
            <div class="dialog-content">
                <header class="dialog-content-header">
                    <el-input
                        v-model="dialogSearchParams.resourceName"
                        placeholder="按资源名称检索"
                        clearable
                    />
                    <el-input
                        v-model="dialogSearchParams.resourceDescription"
                        placeholder="按资源描述检索"
                        clearable
                    />
                    <el-button type="primary" @click.native.stop="searchResources">查询</el-button>
                    <el-radio-group class="header-radio-group" size="small" v-model="radioValue">
                        <el-radio-button
                            v-for="(item, index) in radioGroup"
                            :key="index"
                            :label="item.value"
                        >
                            {{ item.label }}
                        </el-radio-button>
                    </el-radio-group>
                </header>
                <main class="dialog-content-main">
                    <el-checkbox
                        :indeterminate="isIndeterminate"
                        v-model="checkAll"
                        @change="handleCheckAllChange"
                        >资源
                    </el-checkbox>
                    <div
                        class="resource-list custom-scrollbar"
                        v-if="displayResourceList.length > 0"
                    >
                        <div
                            class="resource-item"
                            :class="{ 'is-choice': item.isChoice }"
                            v-for="(item, index) in displayResourceList"
                            :key="index"
                        >
                            <el-checkbox
                                v-model="item.isChoice"
                                @change="handleResourceCheckChange"
                                >{{ item.resourceName }}</el-checkbox
                            >
                            <p
                                class="resource-desc text-ellipsis"
                                :title="item.resourceDescription"
                            >
                                {{ item.resourceDescription }}
                            </p>
                        </div>
                    </div>
                    <el-empty
                        v-else
                        description="暂无数据"
                        :image="require('@/img/common/noDataMon.png')"
                        style="width: 100%; height: 100%"
                    >
                    </el-empty>
                </main>
            </div>
            <div class="dialog-footer">
                <el-button @click.native.stop="resourceDialogVisible = false">取消</el-button>
                <el-button type="primary" @click.native.stop="handleAddResource">确定</el-button>
            </div>
        </el-dialog>
    </div>
</template>

<script>
import ConfigResource from '@/script/api/module/config-resource';
import ConfigService from '@/script/api/module/config-service';

export default {
    name: 'ServiceResourceInfo',
    props: {
        value: {
            type: Object,
            default: () => {}
        },
        isEdit: {
            type: Boolean,
            default: false
        },
        readonly: {
            type: Boolean,
            default: false
        }
    },
    data() {
        return {
            // 全局存储已选资源ID
            selectedResourceIds: [],
            expandList: [],
            titleIcon: {
                0: require('@/img/configPage/icon-close.png'),
                1: require('@/img/configPage/icon-open.png')
            },
            resourceDetail: {},
            // 关联资源弹窗
            resourceDialogVisible: false,
            dialogSearchParams: {
                resourceName: '',
                resourceDescription: ''
            },
            radioValue: 'all',
            radioGroup: [
                { label: '全部', value: 'all' },
                { label: '已选', value: 'select' }
            ],
            isIndeterminate: false,
            checkAll: false,
            dialogResourceList: [],
            // 存储筛选后的资源列表
            filteredResourceList: []
        };
    },
    computed: {
        displayResourceList() {
            return this.filteredResourceList;
        }
    },
    watch: {
        radioValue() {
            this.fetchResourceList();
        }
    },
    methods: {
        /**
         * 同步 selectedResourceIds 与当前 dialogResourceList 的状态
         */
        syncSelectedResourceIds() {
            const idSet = new Set(this.selectedResourceIds);
            this.dialogResourceList.forEach((item) => {
                if (item.isChoice) {
                    idSet.add(item.resourceId);
                } else {
                    idSet.delete(item.resourceId);
                }
            });
            this.selectedResourceIds = Array.from(idSet);
        },
        handleExpandClick(resourceId) {
            ConfigResource.getResourceDetails({ resourceId }).then((res) => {
                let resourceAnnotationsPriority = '最重要';
                if (!res.data.resourceAnnotationsPriority) {
                    resourceAnnotationsPriority = '不重要';
                }
                this.resourceDetail = {
                    资源路由路径: res.data.resourcePath,
                    资源文件路径: res.data.resourceUri,
                    资源类型: res.data.resourceMimeType,
                    资源适用角色: res.data.resourceAnnotationsAudience,
                    资源重要程度: resourceAnnotationsPriority,
                    创建时间: res.data.createdTime,
                    更新时间: res.data.lastUpdateTime,
                    其他备注: res.data.comment
                };
            });
            if (this.expandList.includes(resourceId)) {
                this.expandList = this.expandList.filter((id) => id !== resourceId);
            } else {
                this.expandList = [resourceId];
            }
        },
        handleDelete(resourceId) {
            const h = this.$createElement;
            this.$confirm('', {
                message: h('div', { style: 'display: flex; flex-direction: column; gap: 8px;' }, [
                    h('div', { style: 'display: flex; align-items: center; gap: 8px;' }, [
                        h('i', {
                            class: 'el-icon-warning',
                            style: 'color:#f90;font-size:24px;line-height:24px;'
                        }),
                        h('span', { class: 'mcpservice-theme confirm-title' }, '确定删除该资源吗？')
                    ]),
                    h(
                        'p',
                        { class: 'mcpservice-theme confirm-desc', style: 'margin: 0;' },
                        '删除后将无法恢复'
                    )
                ]),
                confirmButtonText: '确定',
                cancelButtonText: '取消'
            }).then(() => {
                ConfigService.delMCPResourceRelation({
                    mcpId: this.value.mcpId,
                    resourceIds: [resourceId]
                }).then((res) => {
                    if (res.serviceFlag === 'TRUE') {
                        this.$message.success(res.returnMsg || '删除成功');
                        this.$emit('updateCard');
                    } else {
                        this.$message.error(res.returnMsg || '删除失败');
                    }
                });
            });
        },
        openResourceDialog() {
            this.resourceDialogVisible = true;
            // 初始化已选集合
            this.selectedResourceIds = (this.value.resourceList || []).map(
                (item) => item.resourceId
            );
            this.fetchResourceList();
        },
        fetchResourceList() {
            ConfigService.getMCPResourceConfigList({
                mcpId: this.value.mcpId,
                resourceName: this.dialogSearchParams.resourceName,
                resourceDescription: this.dialogSearchParams.resourceDescription,
                isChoice: {
                    all: 0,
                    select: 1
                }[this.radioValue],
                pageNum: 1,
                pageSize: 999
            }).then((res) => {
                // 使用全局已选集合矫正勾选
                this.dialogResourceList = res.data.list.map((item) => ({
                    ...item,
                    isChoice: this.selectedResourceIds.includes(item.resourceId) || !!item.isChoice
                }));
                this.filteredResourceList = [...this.dialogResourceList];
                this.updateSelectAllState();
            });
        },
        handleCheckAllChange(val) {
            this.displayResourceList.forEach((item) => {
                item.isChoice = val;
            });
            this.syncSelectedResourceIds();
            this.updateSelectAllState();
        },
        handleResourceCheckChange() {
            this.syncSelectedResourceIds();
            this.updateSelectAllState();
        },
        updateSelectAllState() {
            if (this.displayResourceList.length === 0) {
                this.checkAll = false;
                this.isIndeterminate = false;
                return;
            }

            const checkedCount = this.displayResourceList.filter((item) => item.isChoice).length;
            this.checkAll = checkedCount === this.displayResourceList.length;
            this.isIndeterminate =
                checkedCount > 0 && checkedCount < this.displayResourceList.length;
        },
        searchResources() {
            this.fetchResourceList();
        },
        handleAddResource() {
            // 使用全局集合，确保所有已选资源都提交
            const selectedResources = this.selectedResourceIds;

            ConfigService.saveMCPResourceConfig({
                mcpId: this.value.mcpId,
                resourceIds: selectedResources
            }).then((res) => {
                if (res.serviceFlag === 'TRUE') {
                    this.$message.success(res.returnMsg || '添加成功');
                    this.resourceDialogVisible = false;
                    this.$emit('updateCard');
                } else {
                    this.$message.error(res.returnMsg || '添加失败');
                }
            });
        }
    }
};
</script>

<style lang="less" scoped>
.resource-info-content {
    display: flex;
    gap: 1rem;
    flex-direction: column;
    .content-header {
        height: 2rem;
        display: flex;
        align-items: center;
        justify-content: space-between;
        span {
            font-family:
                PingFangSC,
                PingFang SC;
            font-weight: 600;
            font-size: 1rem;
            color: rgba(0, 0, 0, 0.85);
        }

        .header-btn {
            margin: 0;
            background: #ffffff;
            border-radius: 0.25rem;
            border: 0.0625rem solid #1565ff99;
            color: #1565ff;
            &:hover {
                background: #1565ff1a;
            }
        }
    }
    .content-main {
        min-height: 0;
        flex: 1;
        display: flex;
        flex-direction: column;
        gap: 1rem;
        overflow-y: auto;
        .main-item {
            flex: none;
            height: 5rem;
            background: #f6f8fa;
            border: 0.0625rem solid #f6f8fa;
            border-radius: 0.375rem;
            padding: 1rem 1.5rem;
            display: flex;
            flex-direction: column;
            gap: 0.5rem;
            cursor: pointer;
            & > *:not(header) {
                cursor: auto;
            }
            &.active {
                height: max-content;
                background: #f2f5ff;
                border-radius: 0.375rem;
                border: 0.0625rem solid rgba(21, 101, 255, 0.1);
            }
            header {
                display: flex;
                align-items: center;
                justify-content: space-between;
                gap: 2rem;
                .header-left {
                    display: flex;
                    flex-direction: column;
                    justify-content: space-around;
                    gap: 0.25rem;
                    .title {
                        display: flex;
                        align-items: center;
                        gap: 0.5rem;
                        &-icon {
                            width: 2.5rem;
                            height: 1.25rem;
                        }
                        &-text {
                            font-family:
                                PingFangSC,
                                PingFang SC;
                            font-weight: 600;
                            font-size: 1rem;
                            color: rgba(0, 0, 0, 0.85);
                            line-height: 1.25rem;
                        }
                    }
                    .desc {
                        font-family:
                            PingFangSC,
                            PingFang SC;
                        font-weight: 400;
                        font-size: 0.75rem;
                        color: rgba(0, 0, 0, 0.65);
                        line-height: 1.25rem;
                    }
                }
                .header-right {
                    display: flex;
                    align-items: center;
                    gap: 1rem;
                    .delete-btn {
                        width: 2rem;
                        height: 2rem;
                        background: #ffffff;
                        border-radius: 0.25rem;
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        cursor: pointer;
                        .el-icon-delete {
                            font-size: 1rem;
                            color: rgba(0, 0, 0, 0.65);
                        }
                        &:hover {
                            .el-icon-delete {
                                color: #f74041;
                            }
                        }
                    }
                    .dropdown-btn {
                        width: 2rem;
                        height: 2rem;
                        background: #ffffff;
                        border-radius: 0.25rem;
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        .header-expand-icon {
                            font-size: 1rem;
                            color: rgba(0, 0, 0, 0.65);
                            cursor: pointer;
                            transition: transform 0.3s ease-in-out;
                            &.rotate180 {
                                transform: rotate(-180deg);
                            }
                        }
                    }
                }
            }
            main {
                min-height: 0;
                flex: 1;
                .main-content {
                    width: 100%;
                    height: 100%;
                    display: grid;
                    grid-template-columns: repeat(4, 1fr);
                    gap: 1rem;
                    .main-content-item {
                        width: 100%;
                        height: 2.75rem;
                        background: #ffffff;
                        border-radius: 0.375rem;
                        display: flex;
                        align-items: center;
                        justify-content: start;
                        padding: 0.5rem 1rem;
                        p {
                            display: flex;
                            align-items: center;
                            margin: 0;
                            &::before {
                                content: '';
                                display: block;
                                width: 0.375rem;
                                height: 0.375rem;
                                background: #1565ff;
                                border-radius: 50%;
                                margin-right: 0.5rem;
                            }
                            span {
                                font-family:
                                    PingFangSC,
                                    PingFang SC;
                                font-weight: 400;
                                font-size: 0.875rem;
                                color: rgba(0, 0, 0, 0.85);
                            }
                        }
                        &-key {
                            width: auto;
                            flex: none;
                        }
                        &-value {
                            flex: 1;
                        }
                    }
                }
            }
        }
    }
    .dialog-content {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: space-between;
        gap: 16px;
        .dialog-content-header {
            width: 100%;
            display: flex;
            align-items: center;
            gap: 1rem;
            .el-input {
                height: 32px;
                width: 280px;
                /deep/.el-input__inner {
                    height: 100%;
                }
            }
            .header-radio-group {
                margin-left: auto;
                height: 32px;
                /deep/.el-radio-button {
                    margin: 0;
                    height: 100%;
                    &.is-active {
                        .el-radio-button__inner {
                            font-weight: 500;
                            color: #1565ff;
                            border-color: #1565ff;
                            background-color: #1565ff1a;
                            border-radius: 4px;
                        }
                    }
                    &__inner {
                        font-family:
                            PingFangSC,
                            PingFang SC;
                        font-weight: 400;
                        font-size: 14px;
                        color: rgba(0, 0, 0, 0.65);
                        line-height: 20px;
                        padding: 0 20px;
                        height: 100%;
                        display: flex;
                        align-items: center;
                        justify-content: center;
                    }
                }
            }
        }
        .dialog-content-main {
            width: 100%;
            height: 400px;
            display: flex;
            flex-direction: column;
            gap: 16px;
            /deep/.el-checkbox {
                &__input {
                    &.is-focus {
                        .el-checkbox__inner {
                            border-color: #1565ff;
                        }
                    }
                    &.is-checked {
                        .el-checkbox__inner {
                            background-color: #1565ff;
                            border-color: #1565ff;
                        }
                    }
                    &.is-indeterminate {
                        .el-checkbox__inner {
                            background-color: #1565ff;
                            border-color: #1565ff;
                        }
                    }
                }
                &__label {
                    font-family:
                        PingFangSC,
                        PingFang SC;
                    font-weight: 500;
                    font-size: 14px;
                    color: rgba(0, 0, 0, 0.85);
                    line-height: 20px;
                }
            }
            .resource-list {
                width: 100%;
                min-height: 0;
                flex: 1;
                overflow-y: auto;
                display: grid;
                grid-template-columns: repeat(3, 1fr);
                grid-auto-rows: 98px;
                gap: 16px;
                .resource-item {
                    width: 100%;
                    height: 100%;
                    padding: 16px;
                    background: #f6f8fa;
                    border-radius: 4px;
                    border: 1px solid transparent;
                    &.is-choice {
                        background: #ffffff;
                        box-shadow:
                            0px 5px 12px 4px rgba(0, 0, 0, 0.06),
                            0px 3px 4px 0px rgba(0, 0, 0, 0.08),
                            0px 1px 2px -2px rgba(0, 0, 0, 0.1);
                        border-color: rgba(21, 101, 255, 0.3);
                    }
                    .resource-desc {
                        --line-clamp: 2;
                        margin: 0 0 0 24px;
                        font-family:
                            PingFangSC,
                            PingFang SC;
                        font-weight: 400;
                        font-size: 14px;
                        color: rgba(0, 0, 0, 0.65);
                        line-height: 20px;
                    }
                }
            }
        }
    }
    .dialog-footer {
        text-align: right;
        margin-top: 32px;
    }
}
</style>
