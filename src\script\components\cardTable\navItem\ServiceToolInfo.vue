<template>
    <div class="tool-info-content">
        <header class="content-header">
            <span>可用工具</span>
            <el-button
                v-if="!readonly"
                type="plain"
                class="header-btn"
                icon="el-icon-link"
                @click="openToolDialog"
            >
                关联工具
            </el-button>
        </header>
        <main class="content-main custom-scrollbar" v-if="value.toolList.length > 0">
            <div
                class="main-item"
                :class="{ active: expandList.includes(item.toolId) }"
                v-for="(item, index) in value.toolList"
                :key="index"
                @click.stop="handleExpandClick(item.toolId)"
            >
                <header @click.stop="handleExpandClick(item.toolId)">
                    <div class="header-left">
                        <div class="title">
                            <img class="title-icon" :src="titleIcon[item.isValid]" alt="" />
                            <span class="title-text">{{ item.toolName }}</span>
                        </div>
                        <div
                            class="desc"
                            :class="{ 'text-ellipsis': !expandList.includes(item.toolId) }"
                            :title="item.toolDescription"
                        >
                            {{ item.toolDescription }}
                        </div>
                    </div>
                    <div class="header-right">
                        <div
                            v-if="!readonly"
                            class="delete-btn"
                            @click.stop="handleDelete(item.toolId)"
                        >
                            <i class="el-icon-delete"></i>
                        </div>
                        <div class="dropdown-btn">
                            <i
                                class="el-icon-arrow-down header-expand-icon"
                                :class="{ rotate180: expandList.includes(item.toolId) }"
                                @click.stop="handleExpandClick(item.toolId)"
                            ></i>
                        </div>
                    </div>
                </header>
                <main v-if="expandList.includes(item.toolId)" @click.stop>
                    <div class="main-left">
                        <p>入参信息</p>
                        <DocTable
                            v-model="inParam.data"
                            :config="{
                                mode: 'keyField',
                                labelWidth: 150,
                                keyField: 'paramName',
                                valueFields: inParam.columns
                            }"
                            v-if="inParam.data.length > 0"
                        />
                        <el-empty
                            v-else
                            description="暂无数据"
                            :image="require('@/img/common/noDataMon.png')"
                            style="width: 100%; height: 100%"
                        >
                        </el-empty>
                    </div>
                    <div class="main-right">
                        <p>出参信息</p>
                        <DocTable
                            v-model="outParam.data"
                            :config="{
                                mode: 'keyField',
                                labelWidth: 150,
                                keyField: 'paramName',
                                valueFields: outParam.columns
                            }"
                            v-if="outParam.data.length > 0"
                        />
                        <el-empty
                            v-else
                            description="暂无数据"
                            :image="require('@/img/common/noDataMon.png')"
                            style="width: 100%; height: 100%"
                        >
                        </el-empty>
                    </div>
                </main>
            </div>
        </main>
        <el-empty
            v-else
            description="暂无数据"
            :image="require('@/img/common/noDataMon.png')"
            style="width: 100%; height: 100%"
        >
        </el-empty>
        <!-- 关联工具弹窗 -->
        <el-dialog
            title="MCP服务关联工具配置"
            :visible.sync="toolDialogVisible"
            width="1280px"
            :close-on-click-modal="false"
            destroy-on-close
        >
            <div class="dialog-content">
                <header class="dialog-content-header">
                    <el-input
                        v-model="dialogSearchParams.toolName"
                        placeholder="按工具名称检索"
                        clearable
                    />
                    <el-input
                        v-model="dialogSearchParams.toolDescription"
                        placeholder="按工具描述检索"
                        clearable
                    />
                    <el-button type="primary" @click.native.stop="searchTools">查询</el-button>
                    <el-radio-group class="header-radio-group" size="small" v-model="radioValue">
                        <el-radio-button
                            v-for="(item, index) in radioGroup"
                            :key="index"
                            :label="item.value"
                        >
                            {{ item.label }}
                        </el-radio-button>
                    </el-radio-group>
                </header>
                <main class="dialog-content-main">
                    <el-checkbox
                        :indeterminate="isIndeterminate"
                        v-model="checkAll"
                        @change="handleCheckAllChange"
                        >工具
                    </el-checkbox>
                    <div class="tool-list custom-scrollbar" v-if="displayToolList.length > 0">
                        <div
                            class="tool-item"
                            :class="{ 'is-choice': item.isChoice }"
                            v-for="(item, index) in displayToolList"
                            :key="index"
                        >
                            <el-checkbox v-model="item.isChoice" @change="handleToolCheckChange">{{
                                item.toolName
                            }}</el-checkbox>
                            <p class="tool-desc text-ellipsis" :title="item.toolDescription">
                                {{ item.toolDescription }}
                            </p>
                        </div>
                    </div>
                    <el-empty
                        v-else
                        description="暂无数据"
                        :image="require('@/img/common/noDataMon.png')"
                        style="width: 100%; height: 100%"
                    >
                    </el-empty>
                </main>
            </div>
            <div class="dialog-footer">
                <el-button @click.native.stop="toolDialogVisible = false">取消</el-button>
                <el-button type="primary" @click.native.stop="handleAddTool">确定</el-button>
            </div>
        </el-dialog>
    </div>
</template>

<script>
import DocTable from '@/script/components/tables/DocTable.vue';
import ConfigService from '@/script/api/module/config-service';

export default {
    name: 'ServiceToolInfo',
    components: {
        DocTable
    },
    props: {
        value: {
            type: Object,
            default: () => {}
        },
        isEdit: {
            type: Boolean,
            default: false
        },
        readonly: {
            type: Boolean,
            default: false
        }
    },
    data() {
        return {
            // 用于全局存储已选择工具 ID
            selectedToolIds: [],
            expandList: [],
            titleIcon: {
                0: require('@/img/configPage/icon-close.png'),
                1: require('@/img/configPage/icon-open.png')
            },
            toolParamInfoList: [],
            // 关联工具弹窗
            toolDialogVisible: false,
            dialogSearchParams: {
                toolName: '',
                toolDescription: ''
            },
            radioValue: 'all',
            radioGroup: [
                { label: '全部', value: 'all' },
                { label: '已选', value: 'select' }
            ],
            isIndeterminate: false,
            checkAll: false,
            dialogToolList: [],
            // 存储筛选后的工具列表
            filteredToolList: []
        };
    },
    computed: {
        inParam() {
            return {
                columns: [
                    { prop: 'paramType', label: '参数类型' },
                    { prop: 'paramDefaultValue', label: '参数默认值' },
                    { prop: 'paramDescription', label: '参数描述' },
                    { prop: 'paramJsonPath', label: '参数路径' },
                    { prop: 'paramComment', label: '其它备注' }
                ],
                data: this.toolParamInfoList.filter((item) => item.paramInout === 'IN')
            };
        },
        outParam() {
            return {
                columns: [
                    { prop: 'paramType', label: '参数类型' },
                    { prop: 'paramDefaultValue', label: '参数默认值' },
                    { prop: 'paramDescription', label: '参数描述' },
                    { prop: 'paramJsonPath', label: '参数路径' },
                    { prop: 'paramComment', label: '其它备注' }
                ],
                data: this.toolParamInfoList.filter((item) => item.paramInout === 'OUT')
            };
        },
        // 实际显示的工具列表，根据筛选条件过滤
        displayToolList() {
            return this.filteredToolList;
        }
    },
    watch: {
        radioValue() {
            this.fetchToolList();
        }
    },
    methods: {
        /**
         * 同步 selectedToolIds 与当前 dialogToolList 的选中状态。
         *  保证用户在当前展示列表中的选择/取消都会正确映射到全局选中集合中。
         */
        syncSelectedToolIds() {
            const idSet = new Set(this.selectedToolIds);
            this.dialogToolList.forEach((item) => {
                if (item.isChoice) {
                    idSet.add(item.toolId);
                } else {
                    idSet.delete(item.toolId);
                }
            });
            this.selectedToolIds = Array.from(idSet);
        },
        handleExpandClick(toolId) {
            ConfigService.getToolDetails({ toolId }).then((res) => {
                this.toolParamInfoList = res.data.toolParamInfoList;
            });
            if (this.expandList.includes(toolId)) {
                this.expandList = this.expandList.filter((id) => id !== toolId);
            } else {
                this.expandList = [toolId];
            }
        },
        handleDelete(toolId) {
            const h = this.$createElement;
            this.$confirm('', {
                message: h('div', { style: 'display: flex; flex-direction: column; gap: 8px;' }, [
                    h('div', { style: 'display: flex; align-items: center; gap: 8px;' }, [
                        h('i', {
                            class: 'el-icon-warning',
                            style: 'color:#f90;font-size:24px;line-height:24px;'
                        }),
                        h('span', { class: 'mcpservice-theme confirm-title' }, '确定删除该工具吗？')
                    ]),
                    h(
                        'p',
                        { class: 'mcpservice-theme confirm-desc', style: 'margin: 0;' },
                        '删除后将无法恢复'
                    )
                ]),
                confirmButtonText: '确定',
                cancelButtonText: '取消'
            }).then(() => {
                ConfigService.delMCPToolRelation({
                    mcpId: this.value.mcpId,
                    toolIds: [toolId]
                }).then((res) => {
                    if (res.serviceFlag === 'TRUE') {
                        this.$message.success(res.returnMsg || '删除成功');
                        this.$emit('updateCard');
                    } else {
                        this.$message.error(res.returnMsg || '删除失败');
                    }
                });
            });
        },
        openToolDialog() {
            this.toolDialogVisible = true;
            // 初始化已选工具集合（来自卡片已有的工具列表）
            this.selectedToolIds = (this.value.toolList || []).map((item) => item.toolId);
            this.fetchToolList();
        },
        fetchToolList() {
            ConfigService.getMCPToolConfigList({
                mcpId: this.value.mcpId,
                toolName: this.dialogSearchParams.toolName,
                toolDescription: this.dialogSearchParams.toolDescription,
                isChoice: {
                    all: 0,
                    select: 1
                }[this.radioValue],
                pageNum: 1,
                pageSize: 999
            }).then((res) => {
                // 先根据接口返回构造列表，再根据全局已选集合矫正选中状态
                this.dialogToolList = res.data.list.map((item) => ({
                    ...item,
                    isChoice: this.selectedToolIds.includes(item.toolId) || !!item.isChoice
                }));
                this.filteredToolList = [...this.dialogToolList];
                this.updateSelectAllState();
            });
        },
        handleCheckAllChange(val) {
            this.displayToolList.forEach((item) => {
                item.isChoice = val;
            });
            // 更新全局选中集合
            this.syncSelectedToolIds();
            this.updateSelectAllState();
        },
        handleToolCheckChange() {
            // 单个复选框变化
            this.syncSelectedToolIds();
            this.updateSelectAllState();
        },
        updateSelectAllState() {
            if (this.displayToolList.length === 0) {
                this.checkAll = false;
                this.isIndeterminate = false;
                return;
            }

            const checkedCount = this.displayToolList.filter((item) => item.isChoice).length;
            this.checkAll = checkedCount === this.displayToolList.length;
            this.isIndeterminate = checkedCount > 0 && checkedCount < this.displayToolList.length;
        },
        searchTools() {
            this.fetchToolList();
        },
        handleAddTool() {
            // 使用全局 selectedToolIds，确保包含所有已选工具
            const selectedTools = this.selectedToolIds;

            ConfigService.saveMCPToolConfig({
                mcpId: this.value.mcpId,
                toolIds: selectedTools
            }).then((res) => {
                if (res.serviceFlag === 'TRUE') {
                    this.$message.success(res.returnMsg || '添加成功');
                    this.toolDialogVisible = false;
                    this.$emit('updateCard');
                } else {
                    this.$message.error(res.returnMsg || '添加失败');
                }
            });
        }
    }
};
</script>

<style lang="less" scoped>
.tool-info-content {
    display: flex;
    gap: 1rem;
    flex-direction: column;
    .content-header {
        height: 2rem;
        display: flex;
        align-items: center;
        justify-content: space-between;
        span {
            font-family:
                PingFangSC,
                PingFang SC;
            font-weight: 600;
            font-size: 1rem;
            color: rgba(0, 0, 0, 0.85);
        }

        .header-btn {
            margin: 0;
            background: #ffffff;
            border-radius: 0.25rem;
            border: 0.0625rem solid #1565ff99;
            color: #1565ff;
            &:hover {
                background: #1565ff1a;
            }
        }
    }
    .content-main {
        min-height: 0;
        flex: 1;
        display: flex;
        flex-direction: column;
        gap: 1rem;
        overflow-y: auto;
        .main-item {
            flex: none;
            height: 5rem;
            background: #f6f8fa;
            border: 0.0625rem solid #f6f8fa;
            border-radius: 0.375rem;
            padding: 1rem 1.5rem;
            display: flex;
            flex-direction: column;
            gap: 0.5rem;
            cursor: pointer;
            & > *:not(header) {
                cursor: auto;
            }
            &.active {
                height: max-content;
                background: #f2f5ff;
                border-radius: 0.375rem;
                border: 0.0625rem solid rgba(21, 101, 255, 0.1);
            }
            header {
                display: flex;
                align-items: center;
                justify-content: space-between;
                gap: 2rem;
                .header-left {
                    display: flex;
                    flex-direction: column;
                    justify-content: space-around;
                    gap: 0.25rem;
                    .title {
                        display: flex;
                        align-items: center;
                        gap: 0.5rem;
                        &-icon {
                            width: 2.5rem;
                            height: 1.25rem;
                        }
                        &-text {
                            font-family:
                                PingFangSC,
                                PingFang SC;
                            font-weight: 600;
                            font-size: 1rem;
                            color: rgba(0, 0, 0, 0.85);
                            line-height: 1.25rem;
                        }
                    }
                    .desc {
                        font-family:
                            PingFangSC,
                            PingFang SC;
                        font-weight: 400;
                        font-size: 0.75rem;
                        color: rgba(0, 0, 0, 0.65);
                        line-height: 1.25rem;
                    }
                }
                .header-right {
                    display: flex;
                    align-items: center;
                    gap: 1rem;
                    .delete-btn {
                        width: 2rem;
                        height: 2rem;
                        background: #ffffff;
                        border-radius: 0.25rem;
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        cursor: pointer;
                        .el-icon-delete {
                            font-size: 1rem;
                            color: rgba(0, 0, 0, 0.65);
                        }
                        &:hover {
                            .el-icon-delete {
                                color: #f74041;
                            }
                        }
                    }
                    .dropdown-btn {
                        width: 2rem;
                        height: 2rem;
                        background: #ffffff;
                        border-radius: 0.25rem;
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        .header-expand-icon {
                            font-size: 1rem;
                            color: rgba(0, 0, 0, 0.65);
                            cursor: pointer;
                            transition: transform 0.3s ease-in-out;
                            &.rotate180 {
                                transform: rotate(-180deg);
                            }
                        }
                    }
                }
            }
            main {
                min-height: 0;
                flex: 1;
                display: flex;
                gap: 1rem;
                .main-left,
                .main-right {
                    flex: 1;
                    background: #ffffff;
                    border-radius: 0.375rem;
                    padding: 1rem;
                    p {
                        font-family:
                            PingFangSC,
                            PingFang SC;
                        font-weight: 600;
                        font-size: 0.875rem;
                        color: rgba(0, 0, 0, 0.85);
                        line-height: 1.25rem;
                        margin: 0 0 1rem;
                    }
                }
            }
        }
    }
    .dialog-content {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: space-between;
        gap: 16px;
        .dialog-content-header {
            width: 100%;
            display: flex;
            align-items: center;
            gap: 1rem;
            .el-input {
                height: 32px;
                width: 280px;
                /deep/.el-input__inner {
                    height: 100%;
                }
            }
            .header-radio-group {
                margin-left: auto;
                height: 32px;
                /deep/.el-radio-button {
                    margin: 0;
                    height: 100%;
                    &.is-active {
                        .el-radio-button__inner {
                            font-weight: 500;
                            color: #1565ff;
                            border-color: #1565ff;
                            background-color: #1565ff1a;
                            border-radius: 4px;
                        }
                    }
                    &__inner {
                        font-family:
                            PingFangSC,
                            PingFang SC;
                        font-weight: 400;
                        font-size: 14px;
                        color: rgba(0, 0, 0, 0.65);
                        line-height: 20px;
                        padding: 0 20px;
                        height: 100%;
                        display: flex;
                        align-items: center;
                        justify-content: center;
                    }
                }
            }
        }
        .dialog-content-main {
            width: 100%;
            height: 400px;
            display: flex;
            flex-direction: column;
            gap: 16px;
            /deep/.el-checkbox {
                &__input {
                    &.is-focus {
                        .el-checkbox__inner {
                            border-color: #1565ff;
                        }
                    }
                    &.is-checked {
                        .el-checkbox__inner {
                            background-color: #1565ff;
                            border-color: #1565ff;
                        }
                    }
                    &.is-indeterminate {
                        .el-checkbox__inner {
                            background-color: #1565ff;
                            border-color: #1565ff;
                        }
                    }
                }
                &__label {
                    font-family:
                        PingFangSC,
                        PingFang SC;
                    font-weight: 500;
                    font-size: 14px;
                    color: rgba(0, 0, 0, 0.85);
                    line-height: 20px;
                }
            }
            .tool-list {
                width: 100%;
                min-height: 0;
                flex: 1;
                overflow-y: auto;
                display: grid;
                grid-template-columns: repeat(3, 1fr);
                grid-auto-rows: 98px;
                gap: 16px;
                .tool-item {
                    width: 100%;
                    height: 100%;
                    padding: 16px;
                    background: #f6f8fa;
                    border-radius: 4px;
                    border: 1px solid transparent;
                    &.is-choice {
                        background: #ffffff;
                        box-shadow:
                            0px 5px 12px 4px rgba(0, 0, 0, 0.06),
                            0px 3px 4px 0px rgba(0, 0, 0, 0.08),
                            0px 1px 2px -2px rgba(0, 0, 0, 0.1);
                        border-color: rgba(21, 101, 255, 0.3);
                    }
                    .tool-desc {
                        --line-clamp: 2;
                        margin: 0 0 0 24px;
                        font-family:
                            PingFangSC,
                            PingFang SC;
                        font-weight: 400;
                        font-size: 14px;
                        color: rgba(0, 0, 0, 0.65);
                        line-height: 20px;
                    }
                }
            }
        }
    }
    .dialog-footer {
        text-align: right;
        margin-top: 32px;
    }
}
</style>
